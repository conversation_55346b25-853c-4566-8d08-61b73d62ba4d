# ACF Custom Post Type Importer

A comprehensive WordPress plugin for importing ACF (Advanced Custom Fields) field values to any custom post type from CSV format. Perfect companion to the ACF Custom Post Type Exporter plugin.

## Features

- **Universal Post Type Support**: Import to any custom post type including WooCommerce products, custom post types, and standard WordPress posts/pages
- **Intelligent Field Mapping**: Visual interface to map CSV columns to WordPress/ACF fields
- **Auto-Mapping**: Automatically suggests field mappings based on column names
- **Flexible Content Support**: Automatically creates missing flexible content items when ACF fields are empty
- **Data Validation**: Validates data before import with detailed error reporting
- **Batch Processing**: Handles large datasets with configurable batch sizes
- **Multiple Import Modes**: Create new posts, update existing posts, or both
- **Progress Tracking**: Real-time progress updates during import
- **Media Import**: Automatically imports images and files from URLs
- **Secure Processing**: Nonce verification and user capability checks

## Supported Field Types

### ACF Fields
- Text, Textarea, Number, Email, URL, Password
- Select, Checkbox, Radio, True/False
- Date Picker, Date Time Picker, Time Picker
- Color Picker, Range
- Image, File, Gallery (imports from URLs)
- Post Object, Relationship
- Taxonomy, User
- WYSIWYG Editor, oEmbed
- Repeater fields (JSON format)
- Group fields (JSON format)
- **Flexible Content fields** (with automatic layout creation)

### Standard WordPress Fields
- Post ID (for updates), Title, Content, Excerpt
- Post Status, Date Published, Author
- Featured Image (from URL or attachment ID)
- Menu Order
- Taxonomies (categories, tags, custom taxonomies)

## Installation

1. Upload the plugin files to `/wp-content/plugins/acf-custom-post-importer/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to Tools > ACF Post Importer to start using the plugin

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Advanced Custom Fields (ACF) plugin (free or pro version)
- Sufficient server memory for large imports

## Usage

### Step 1: Prepare Your CSV File
1. Create a CSV file with proper column headers
2. Ensure UTF-8 encoding for special characters
3. First row must contain column headers
4. Maximum file size: 10MB (configurable)

### Step 2: Upload and Preview
1. Go to Tools > ACF Post Importer
2. Upload your CSV file
3. Preview the data and column structure
4. Verify total rows and columns detected

### Step 3: Select Post Type and Map Fields
1. Choose the target post type from dropdown
2. Map CSV columns to WordPress/ACF fields
3. Use auto-mapping for quick setup
4. Review field types and requirements

### Step 4: Configure Import Options
1. Choose import mode:
   - **Create new posts only**: Only creates new posts
   - **Update existing posts only**: Updates posts by ID
   - **Create new or update existing**: Creates or updates based on ID
2. Set batch size (recommended: 50 for large datasets)
3. Validate data before import

### Step 5: Import and Monitor
1. Start the import process
2. Monitor real-time progress
3. Review import results and any errors
4. Download error log if needed

## CSV Format Guidelines

### Basic Structure
```csv
post_title,post_content,custom_field_1,custom_field_2
"Sample Title","Sample content","Field Value 1","Field Value 2"
"Another Title","More content","Field Value 3","Field Value 4"
```

### Field Value Formats

**Text Fields**
```csv
title,description
"Product Name","Product description here"
```

**Date Fields**
```csv
event_date,event_time,event_datetime
"2024-12-25","14:30:00","2024-12-25 14:30:00"
```

**Boolean Fields**
```csv
is_featured,is_active
"1","yes"
"0","no"
```

**Select/Checkbox Fields**
```csv
category,tags
"electronics","tag1,tag2,tag3"
```

**Image/File Fields**
```csv
featured_image,gallery
"https://example.com/image.jpg","https://example.com/img1.jpg,https://example.com/img2.jpg"
```

**Post Relationships**
```csv
related_posts,author_id
"123,456,789","5"
```

**Taxonomies**
```csv
product_category,product_tags
"Electronics,Gadgets","new,featured,sale"
```

## Flexible Content Support

The plugin includes special handling for ACF Flexible Content fields with automatic creation of missing layout items.

### Automatic Layout Creation
- If a flexible content field is empty or has no items, the plugin automatically creates a default layout item
- Uses the first available layout from the field configuration
- Populates sub-fields with appropriate default values based on field types

### CSV Format for Flexible Content

**Simple Layout Name (Creates with Default Values)**
```csv
my_flexible_field
"hero_section"
```

**Layout with Sub-field Data**
```csv
my_flexible_field
"hero_section:title=Welcome,subtitle=Get started today"
```

**Multiple Layouts**
```csv
my_flexible_field
"hero_section:title=Welcome|content_section:text=Lorem ipsum,image=123"
```

**JSON Format (Advanced)**
```csv
my_flexible_field
"[{""acf_fc_layout"":""hero_section"",""title"":""Welcome"",""subtitle"":""Get started""}]"
```

### Flexible Content Manager Features
- Detects existing flexible content layouts
- Creates missing layouts with default values
- Updates existing layouts with new data
- Supports nested sub-fields and complex structures
- Handles all ACF field types within layouts

## Import Modes

### Create New Posts Only
- Ignores ID column if present
- Always creates new posts
- Safe for initial imports
- Recommended for new content

### Update Existing Posts Only
- Requires ID column in CSV
- Only updates existing posts
- Skips rows with non-existent IDs
- Good for bulk updates

### Create New or Update Existing
- Creates new posts if no ID provided
- Updates existing posts if ID matches
- Most flexible option
- Ideal for mixed operations

## Error Handling

The plugin provides comprehensive error reporting:

### Validation Errors
- Missing required fields
- Invalid data formats
- Non-existent post types
- Field mapping issues

### Import Errors
- Database connection issues
- File permission problems
- Memory limit exceeded
- Invalid field values

### Error Resolution
1. Review error messages in import results
2. Fix data in CSV file
3. Re-upload and try again
4. Contact support if issues persist

## Performance Optimization

### Large Dataset Tips
1. **Use appropriate batch sizes**:
   - Small datasets (< 1000 rows): 100
   - Medium datasets (1000-5000 rows): 50
   - Large datasets (> 5000 rows): 25

2. **Server considerations**:
   - Increase PHP memory limit
   - Extend execution time limit
   - Run during off-peak hours

3. **Data preparation**:
   - Remove unnecessary columns
   - Optimize image sizes before import
   - Split very large files

### Memory Management
- Plugin automatically manages memory usage
- Processes data in configurable batches
- Cleans up temporary files automatically
- Monitors progress to prevent timeouts

## Troubleshooting

### Common Issues

**"File upload failed"**
- Check file size limits
- Verify file permissions
- Ensure CSV format

**"No data found"**
- Verify CSV has headers in first row
- Check for empty file
- Ensure proper CSV formatting

**"Field mapping errors"**
- Verify post type has ACF fields
- Check field group assignments
- Ensure fields are active

**"Import timeout"**
- Reduce batch size
- Increase server limits
- Split large files

### File Requirements
- CSV format only
- UTF-8 encoding recommended
- Maximum 10MB file size
- Headers in first row required

## Security Features

- **User Capability Checks**: Requires 'manage_options' permission
- **Nonce Verification**: All AJAX requests verified
- **File Validation**: Strict file type and size checking
- **Data Sanitization**: All input data sanitized
- **Secure File Handling**: Temporary files in protected directory

## Hooks and Filters

### Filters
- `acf_cpt_importer_max_file_size` - Modify maximum upload size
- `acf_cpt_importer_batch_size` - Default batch size
- `acf_cpt_importer_field_mapping` - Modify field mapping
- `acf_cpt_importer_import_data` - Modify data before import

### Actions
- `acf_cpt_importer_before_import` - Fired before import starts
- `acf_cpt_importer_after_import` - Fired after import completes
- `acf_cpt_importer_post_imported` - Fired after each post import

## Compatibility

### WordPress Versions
- WordPress 5.0+
- WordPress 6.0+ (recommended)

### ACF Versions
- ACF Free 5.0+
- ACF Pro 5.0+
- ACF Pro 6.0+ (recommended)

### Post Types
- Standard WordPress posts/pages
- WooCommerce products
- Custom post types
- Any public post type

## Support

For support and feature requests:
1. Check the troubleshooting section
2. Review error messages carefully
3. Test with small sample data first
4. Contact development team if needed

## Changelog

### Version 1.0.0
- Initial release
- Support for all major ACF field types including Flexible Content
- **Automatic creation of missing flexible content items**
- Flexible Content Manager for layout handling
- Batch import processing
- Visual field mapping interface
- Data validation and error reporting
- Multiple import modes
- Progress tracking
- Media import from URLs

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by the Hisense Development Team as a companion to the ACF Custom Post Type Exporter plugin.
