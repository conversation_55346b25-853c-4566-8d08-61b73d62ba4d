<?php
/**
 * Post Type Detector Class
 * 
 * Detects and manages available post types for import
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Importer_Post_Type_Detector {
    
    /**
     * Get all available post types for import
     * 
     * @return array
     */
    public function get_available_post_types() {
        $post_types = get_post_types(array('public' => true), 'objects');
        $available_types = array();

        foreach ($post_types as $post_type) {
            // Skip attachment post type
            if ($post_type->name === 'attachment') {
                continue;
            }
            
            $count = wp_count_posts($post_type->name);
            $total_count = 0;

            if (is_object($count)) {
                foreach ($count as $status => $num) {
                    if ($status !== 'trash' && $status !== 'auto-draft') {
                        $total_count += $num;
                    }
                }
            }

            $available_types[$post_type->name] = array(
                'name' => $post_type->name,
                'label' => $post_type->labels->name,
                'singular_label' => $post_type->labels->singular_name,
                'count' => $total_count,
                'supports' => get_all_post_type_supports($post_type->name),
                'taxonomies' => get_object_taxonomies($post_type->name),
                'hierarchical' => $post_type->hierarchical,
                'public' => $post_type->public,
                'show_ui' => $post_type->show_ui
            );
        }

        return $available_types;
    }
    
    /**
     * Check if a post type exists and is valid for import
     * 
     * @param string $post_type
     * @return bool
     */
    public function is_valid_post_type($post_type) {
        if (!post_type_exists($post_type)) {
            return false;
        }
        
        $post_type_object = get_post_type_object($post_type);
        
        // Check if post type is public or has show_ui enabled
        return $post_type_object && ($post_type_object->public || $post_type_object->show_ui);
    }
    
    /**
     * Get post type capabilities
     * 
     * @param string $post_type
     * @return array
     */
    public function get_post_type_capabilities($post_type) {
        $post_type_object = get_post_type_object($post_type);
        
        if (!$post_type_object) {
            return array();
        }
        
        return array(
            'create_posts' => current_user_can($post_type_object->cap->create_posts),
            'edit_posts' => current_user_can($post_type_object->cap->edit_posts),
            'edit_others_posts' => current_user_can($post_type_object->cap->edit_others_posts),
            'publish_posts' => current_user_can($post_type_object->cap->publish_posts),
            'read_private_posts' => current_user_can($post_type_object->cap->read_private_posts)
        );
    }
    
    /**
     * Get supported features for a post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_post_type_supports($post_type) {
        return get_all_post_type_supports($post_type);
    }
    
    /**
     * Get taxonomies associated with a post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_post_type_taxonomies($post_type) {
        $taxonomies = get_object_taxonomies($post_type, 'objects');
        $taxonomy_data = array();
        
        foreach ($taxonomies as $taxonomy) {
            $taxonomy_data[$taxonomy->name] = array(
                'name' => $taxonomy->name,
                'label' => $taxonomy->labels->name,
                'singular_label' => $taxonomy->labels->singular_name,
                'hierarchical' => $taxonomy->hierarchical,
                'public' => $taxonomy->public,
                'show_ui' => $taxonomy->show_ui
            );
        }
        
        return $taxonomy_data;
    }
    
    /**
     * Get meta fields for a post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_post_type_meta_fields($post_type) {
        global $wpdb;
        
        // Get all meta keys used by this post type
        $meta_keys = $wpdb->get_col($wpdb->prepare("
            SELECT DISTINCT meta_key 
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE p.post_type = %s
            AND meta_key NOT LIKE '\_%'
            ORDER BY meta_key
        ", $post_type));
        
        $meta_fields = array();
        foreach ($meta_keys as $meta_key) {
            $meta_fields[$meta_key] = array(
                'name' => $meta_key,
                'label' => ucwords(str_replace('_', ' ', $meta_key)),
                'type' => 'meta'
            );
        }
        
        return $meta_fields;
    }
    
    /**
     * Check if user can import to post type
     * 
     * @param string $post_type
     * @return bool
     */
    public function can_import_to_post_type($post_type) {
        if (!$this->is_valid_post_type($post_type)) {
            return false;
        }
        
        $capabilities = $this->get_post_type_capabilities($post_type);
        
        // User needs at least create and edit capabilities
        return $capabilities['create_posts'] && $capabilities['edit_posts'];
    }
    
    /**
     * Get post type statistics
     * 
     * @param string $post_type
     * @return array
     */
    public function get_post_type_stats($post_type) {
        $counts = wp_count_posts($post_type);
        $stats = array(
            'total' => 0,
            'published' => 0,
            'draft' => 0,
            'private' => 0,
            'pending' => 0,
            'trash' => 0
        );
        
        if (is_object($counts)) {
            foreach ($counts as $status => $count) {
                $stats['total'] += $count;
                if (isset($stats[$status])) {
                    $stats[$status] = $count;
                }
            }
            
            // Subtract trash from total
            $stats['total'] -= $stats['trash'];
        }
        
        return $stats;
    }
    
    /**
     * Get recent posts for a post type (for preview/validation)
     * 
     * @param string $post_type
     * @param int $limit
     * @return array
     */
    public function get_recent_posts($post_type, $limit = 5) {
        $posts = get_posts(array(
            'post_type' => $post_type,
            'posts_per_page' => $limit,
            'post_status' => array('publish', 'draft', 'private'),
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        $recent_posts = array();
        foreach ($posts as $post) {
            $recent_posts[] = array(
                'ID' => $post->ID,
                'title' => $post->post_title,
                'status' => $post->post_status,
                'date' => $post->post_date,
                'author' => get_the_author_meta('display_name', $post->post_author)
            );
        }
        
        return $recent_posts;
    }
}
