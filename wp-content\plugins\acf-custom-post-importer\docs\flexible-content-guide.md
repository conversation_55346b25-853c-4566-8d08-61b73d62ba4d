# Flexible Content Import Guide

This guide explains how to use the ACF Custom Post Importer with Flexible Content fields, including the automatic creation feature for missing layout items.

## Overview

The ACF Custom Post Importer includes a specialized `Flexible Content Manager` that automatically handles ACF Flexible Content fields during import. This feature ensures that:

1. **Missing layouts are automatically created** when a flexible content field is empty
2. **Default values are populated** for all sub-fields based on their field types
3. **Existing layouts can be updated** with new data from CSV
4. **Multiple layout formats are supported** for maximum flexibility

## Automatic Layout Creation

### When Does It Trigger?

The automatic layout creation feature activates when:
- A flexible content field exists in your ACF field group
- The field is mapped in your CSV import
- The current post has no existing flexible content items for that field
- OR the CSV value is empty/null for that field

### What Gets Created?

When automatic creation triggers:
1. The **first available layout** from the field configuration is used
2. **Default values** are assigned to all sub-fields based on their types:
   - Text fields: Empty string
   - Number fields: 0
   - True/False fields: false
   - Date fields: Current date
   - Select fields: First available option
   - And more...

## CSV Format Options

### 1. Simple Layout Name
Creates a layout with default values:
```csv
my_flexible_field
"hero_section"
```

### 2. Layout with Sub-field Data
Creates a layout and populates specific sub-fields:
```csv
my_flexible_field
"hero_section:title=Welcome,subtitle=Get started today,button_text=Learn More"
```

### 3. Multiple Layouts
Creates multiple layout items separated by pipe (|):
```csv
my_flexible_field
"hero_section:title=Welcome|content_section:text=Lorem ipsum,image=123|cta_section:button_text=Contact Us"
```

### 4. JSON Format (Advanced)
For complex data structures:
```csv
my_flexible_field
"[{""acf_fc_layout"":""hero_section"",""title"":""Welcome"",""subtitle"":""Get started""},{""acf_fc_layout"":""content_section"",""text"":""Lorem ipsum""}]"
```

## Field Type Handling

The Flexible Content Manager handles all ACF field types within layouts:

### Text Fields
```csv
layout_name:text_field=Hello World,textarea_field=Long text content here
```

### Number Fields
```csv
layout_name:price=29.99,quantity=5
```

### Boolean Fields
```csv
layout_name:is_featured=1,is_active=true,show_banner=yes
```

### Date Fields
```csv
layout_name:event_date=2024-12-25,event_time=14:30:00,event_datetime=2024-12-25 14:30:00
```

### Select/Choice Fields
```csv
layout_name:category=electronics,priority=high
```

### Image/Media Fields
```csv
layout_name:hero_image=https://example.com/image.jpg,background_image=123
```

## Example Scenarios

### Scenario 1: Empty Flexible Content Field
**CSV Data:**
```csv
post_title,page_builder
"My New Page",""
```

**Result:** 
- A default layout (first available) is automatically created
- All sub-fields get appropriate default values
- Page is ready for editing with basic structure

### Scenario 2: Specific Layout Creation
**CSV Data:**
```csv
post_title,page_builder
"Landing Page","hero_section:title=Welcome,subtitle=Join us today"
```

**Result:**
- Hero section layout is created
- Title and subtitle fields are populated
- Other sub-fields get default values

### Scenario 3: Multiple Layouts
**CSV Data:**
```csv
post_title,page_builder
"Complete Page","hero_section:title=Welcome|content_section:heading=About,text=We are awesome|cta_section:button_text=Get Started"
```

**Result:**
- Three layout items are created in sequence
- Each layout has its specified sub-fields populated
- Remaining sub-fields get default values

## Best Practices

### 1. Field Naming
- Use exact field names as defined in ACF
- Layout names must match exactly (case-sensitive)
- Sub-field names must match exactly

### 2. Data Preparation
- Test with small datasets first
- Validate layout names exist in your field group
- Ensure sub-field names are correct

### 3. Default Value Strategy
- Review default values created by the system
- Consider pre-populating important fields in CSV
- Use the automatic creation as a starting point

### 4. Error Handling
- Check import logs for any flexible content errors
- Verify layouts were created correctly
- Test with sample data before full import

## Troubleshooting

### Common Issues

**Layout not created:**
- Check that the flexible content field exists
- Verify layout name spelling and case
- Ensure field is mapped correctly in import

**Sub-fields not populated:**
- Verify sub-field names match ACF configuration
- Check for typos in field names
- Ensure proper CSV formatting

**Default values unexpected:**
- Review field type configurations in ACF
- Check if custom default values are set
- Consider specifying values explicitly in CSV

### Debug Tips

1. **Enable WordPress Debug Mode:**
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

2. **Check Import Logs:**
- Review the import results for error messages
- Look for flexible content specific warnings

3. **Test with Simple Data:**
- Start with basic layout creation
- Add complexity gradually
- Verify each step works correctly

## Advanced Usage

### Custom Default Values
You can influence default values by:
1. Setting default values in ACF field configuration
2. Using the `acf_cpt_importer_flexible_content_default` filter
3. Specifying all values explicitly in CSV

### Programmatic Control
Developers can extend functionality using:
- `ACF_CPT_Flexible_Content_Manager` class methods
- WordPress hooks and filters
- Custom field value processing

### Integration with Other Plugins
The flexible content manager works with:
- ACF Pro advanced features
- Custom field plugins
- Page builder integrations
- Theme-specific implementations

## Support

For issues specific to flexible content import:
1. Check this guide first
2. Review the main plugin documentation
3. Test with the provided sample CSV
4. Contact support with specific error messages

Remember: The automatic creation feature is designed to provide a solid foundation for your flexible content. You can always edit and enhance the created layouts manually after import.
