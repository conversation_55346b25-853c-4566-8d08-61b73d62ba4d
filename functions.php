<?php
/**
 * define constant
 */
define('THEME_DIR', get_template_directory());
define('THEME_URL', get_template_directory_uri()); 

/**
 * File Loading
*/
$file_loading = array(
    'inc/theme-setup.php',
    'inc/scripts-style.php',
    'inc/general-functions.php', 
    'inc/custom-widgets.php',
    'inc/shortcodes.php',
    'inc/cpt.php'    
);
hisense_loader($file_loading);


function hisense_loader($files) {
  if(!empty($files)):
    foreach ($files as $key => $file): 
      hisense_inclusion($file);
    endforeach;
  endif;
}

/**
 * File inclusion
 *
 * @param string $path
 * @param boolean $include_once
 * @return void
 *
 */
function hisense_inclusion($path, $require = true, $once = true, $return = false) {
    $file_path = locate_template($path);
    if(empty($file_path)) {
	$look_theme = wp_get_theme();
	$html='<h2>'.esc_html__('ERROR:', 'hisense').' </h2>';
	$html.='<p>"'.$look_theme->stylesheet.'/'.$path.'" '.esc_html__('file not exists.', 'hisense').'</p>';
	wp_die($html, esc_html__('File not exists', 'hisense'));
    };
    //default: require_once
    if ($once) :
        if ($require):
            require_once $file_path;
        else :
            include_once $file_path;
        endif;
    else :
        if ($require) :
            require $file_path;
        else :
            if ($return) :
                return include $file_path; 
            else :
                include $file_path;
            endif;
        endif;
    endif;
}

function hisense_enqueue_styles() {
    wp_enqueue_style(
        'hisense-style',
        get_stylesheet_uri(),
        array(),
        '1.16' // Bump this manually
    );
}
add_action('wp_enqueue_scripts', 'hisense_enqueue_styles');


function add_inline_custom_css() {
  echo '<style>
     .wcfmmp_sold_by_container{ display: none !important;}
  </style>';
}
add_action('wp_head', 'add_inline_custom_css');

  
  //this is the function, that will get called by ajax
  function jnz_tnp_ajax_subscribe() {
    check_ajax_referer( 'noncy_nonce', 'nonce' );
    $data = urldecode( $_POST['data'] );
    if ( !empty( $data ) ) :
      $data_array = explode( "&", $data );
      $fields = [];
      foreach ( $data_array as $array ) :
        $array = explode( "=", $array );
        $fields[ $array[0] ] = $array[1];
      endforeach;
    endif;
  
    if ( !empty( $fields ) ) :
      global $wpdb;
      $wpdb->insert( $wpdb->prefix . 'newsletter', array(
           // 'name'          => $fields['nn'],
        'email'         => $fields['ne'],
        'status'        => $fields['na'],
        'http_referer'  => $fields['nhr'],
      )
    );


    $table = $wpdb->prefix . 'newsletter';
        $existing = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE email = %s", $email));

        if ($existing > 0) {
            wp_send_json([
                'status' => 'error',
                'msg'    => __('You are already subscribed.', 'hisense')
            ]);
        }
  
  
      $user_info = get_userdata(get_current_user_id());
      $user_email = $user_info->user_email;
  
      $output = array(
        'status'    => 'success',
        'msg'       => __( 'Thank You! You are successfully subscribed to get our latest updates.', 'hisense' )
      );
  
  
  
      $headers = array('Content-Type: text/html; charset=UTF-8', 'From: Hisense <<EMAIL>>');
  
      $body='<!DOCTYPE html>
      <html>
  
      <head></head>
  
      <body>
      <div id="wrapper" dir="ltr" style="background-color: #c7c4c4 ; margin: 0; padding: 70px 0 70px 0; -webkit-text-size-adjust: none !important; width: 100%;">
      <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%">
      <tbody>
      <tr>
      <td align="center" valign="top">
      <div id="template_header_image">
      <p style="margin-top: 0;"><img width="150px" src="'.get_field('site_logo','options')['url'].'" style="border: none; display: inline; font-size: 14px; font-weight: bold; height: auto; line-height: 100%; outline: none; text-decoration: none; text-transform: capitalize;">
      </p>
  
      </div>
      <table border="0" cellpadding="0" cellspacing="0" width="600" id="template_container" style="box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important; background-color: #fff; border: 1px solid #cfd1c7; border-radius: 3px !important;">
      <tbody>
      <tr>
      <td align="center" valign="top">                                        
  
      <table border="0" cellpadding="0" cellspacing="0" width="600" id="template_header" style="background-color: #0000;border-radius: 3px 3px 0 0 !important;color: #ffffff;border-bottom: 0;font-weight: bold;line-height: 100%;vertical-align: middle;font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;">
      <tbody>
      <tr>
      <td id="header_wrapper" style="padding: 36px 48px; display: block;background-color: #000; color: #fff;">
      <h1 style="color: #fff; font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif; font-size: 30px; font-weight: 300; line-height: 150%; margin: 0; text-align: left; text-shadow: 0 1px 0 #7b8153;">
      Thank You</h1>
      </td>
      </tr>
      </tbody>
      </table>
  
      <!-- End Header -->
      </td>
      </tr>
      <tr>
      <td align="center" valign="top">
      <!-- Body -->
  
      <table border="0" cellpadding="0" cellspacing="0" width="600" id="template_body">
      <tbody>
      <tr>
      <td valign="top" id="body_content" style="background-color: #ffffff;">
      <!-- Content -->
  
      <table border="0" cellpadding="20" cellspacing="0" width="100%">
      <tbody>
      <tr>
      <td valign="top" style="padding: 48px 48px 0;">
      <div id="body_content_inner" style="color: #6c6c6c; font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif; font-size: 14px; line-height: 150%; text-align: left;">
      <table align="center" style="border:3px solid #FFF; width:500px; margin:0 auto; color:#454545; padding: 5px; border-radius: 7px 7px 7px 7px;">
      <tbody>
      This message confirms your subscription to our newsletter. Thank you!
      </tbody>
      </table>
      </div>
      </td>
      </tr>
      </tbody>
      </table>
  
      <!-- End Content -->
      </td>
      </tr>
      </tbody>
      </table>
  
      <!-- End Body -->
      </td>
      </tr>
      <tr>
      <td align="center" valign="top">
      <!-- Footer -->
  
      <table border="0" cellpadding="10" cellspacing="0" width="600" id="template_footer">
      <tbody>
      <tr>
      <td valign="top" style="padding: 0; -webkit-border-radius: 6px;">
      <table border="0" cellpadding="10" cellspacing="0" width="100%">
      <tbody>
      <tr>
      <td colspan="2" valign="middle" id="credit" style="padding: 0 48px 48px 48px; -webkit-border-radius: 6px; border: 0; color: #000; font-family: Arial; font-size: 12px; line-height: 125%; text-align: center;">
  
      </td>
      </tr>
      </tbody>
      </table>
      </td>
      </tr>
      </tbody>
      </table>
  
      <!-- End Footer -->
      </td>
      </tr>
      </tbody>
      </table>
      </td>
      </tr>
      </tbody>
      </table>
      </div>
      </body>
  
      </html>';
      wp_mail($fields['ne'], 'Newsletter Subscription', $body, $headers);
  
  
  
  
  
    else :
      $output = array(
        'status'    => 'error',
        'msg'       => __( 'An Error occurred. Please try again later.', 'hisense' )
      );
    endif;
    wp_send_json( $output );
  }
  add_action( 'wp_ajax_nopriv_ajax_subscribe', 'jnz_tnp_ajax_subscribe' );
  add_action( 'wp_ajax_ajax_subscribe', 'jnz_tnp_ajax_subscribe' );
  


  
include('woocommerce/inc/woo-functions.php');
include('woocommerce/inc/functions/product-year-import.php');

function disable_edit_buttons_for_restricted_users() {
    if (current_user_can('shop_manager')) {
        ?>
        <style>
            /* Hide row actions in the product list */
            .row-actions .edit, .row-actions .inline, .row-actions .trash {
                display: none !important;
            }
            /* Hide "Add New" button on product list page */
/*             .add-new-h2, .page-title-action {
                display: none !important;
            } */
            /* Hide "Publish" and "Update" buttons on the edit product page */
            #publish {
                display: none !important;
            }
        </style>
        <?php
    }
}
add_action('admin_head', 'disable_edit_buttons_for_restricted_users');



add_action('admin_head', 'hide_edit_order_item_for_non_admins');
function hide_edit_order_item_for_non_admins() {
    if (!current_user_can('administrator')) {
        echo '<style>
            .edit-order-item.tips {
                display: none !important;
            }
        </style>';
    }
}

add_action('admin_head', 'disable_calculate_button_for_new_orders_non_admins');

function disable_calculate_button_for_new_orders_non_admins() {
    // Check if we're on a new order page in the admin area
    global $pagenow;

    if ($pagenow === 'post-new.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'shop_order') {
        echo '<style>
            .button.button.save_order.button-primary {
                pointer-events: none;
                opacity: 0.5;
            }
            .recalculate-warning {
                color: #d63638;
                font-size: 12px;
                margin-top: 5px;
            }
        </style>';

        echo '<script>
            jQuery(document).ready(function($) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        // Check if .calculate-action is added to the DOM
                        if ($(".calculate-action").length && !$(".recalculate-warning").length) {
                            // Add a warning message under the recalculate button
                            $("<p class=\'recalculate-warning\'>Please click Recalculate before submitting the order.</p>")
                                .insertAfter(".calculate-action");
                            
                            // Attach click event listener to .calculate-action
                            $(".calculate-action").off("click").on("click", function(e) {
                                e.preventDefault(); 
                                
                                // Change the .save_order button styles to make it active
                                $(".save_order").css({
                                    "pointer-events": "unset",
                                    "opacity": "1"
                                });
                            });
                        }
                    });
                });

                // Observe changes in the body element
                observer.observe(document.body, { childList: true, subtree: true });
            });
        </script>';
    }
}


// add_action( 'wpcf7_before_send_mail', 'process_contact_form_data' );
// function process_contact_form_data( $contact_data ){

//      error_log( 'wpcf7_before_send_mail hook triggered in AJAX mode' );
// error_log( 'Submitted Data: ' . print_r( $contact_data->posted_data, true ) );              
// }

add_action('wpcf7_before_send_mail', 'process_contact_form_data', 10, 1);

function process_contact_form_data($contact_data) {
    $form_id = $contact_data->id();

    // Ensure only the form with ID 17971 triggers the API call
	//     if ($form_id != 18036) {
	//         return;
	//     }
 	if ($form_id != 18036 && $form_id != 18085) {
		return;
	}

    $submission = WPCF7_Submission::get_instance();
    if (!$submission) {
        error_log('Respond.io API Error: No submission data found.');
        return;
    }

    $form_data = $submission->get_posted_data();

    $receiver_name = sanitize_text_field($form_data['reciever-name'] ?? '');
    $phone = sanitize_text_field($form_data['phone2'] ?? '');
	$phone = preg_replace('/\s+/', '', $phone);
    $email         = sanitize_email($form_data['email-2'] ?? '');
    $city          = sanitize_text_field($form_data['city1'] ?? '');
    $relationship  = sanitize_text_field($form_data['relationship'] ?? '');

    $sender_name = sanitize_text_field($form_data['full-name'] ?? '');
    $phone1 = sanitize_text_field($form_data['phone1'] ?? '');
	$phone1 = preg_replace('/\s+/', '', $phone1);
    $email1         = sanitize_email($form_data['email-1'] ?? '');
    $city1          = sanitize_text_field($form_data['city'] ?? '');
    $store  = sanitize_text_field($form_data['store-name'] ?? '');

    if (empty($phone)) {
        error_log('Respond.io API Error: Phone number is required.');
        return;
    }

    $api_url = 'https://api.respond.io/v2/contact/phone:' . urlencode($phone);
    $api_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTEyNzUsInNwYWNlSWQiOjI1MTQ5NCwib3JnSWQiOjI0OTAwNSwidHlwZSI6ImFwaSIsImlhdCI6MTczOTQzOTc1Nn0.ZeSeFfxDnPEPcvFXdGxeMcb2DeuYNCBa1xdqnoR8vHk';

    $body = [
        'firstName'    => $receiver_name,
        'phone'        => $phone,
        'email'        => $email,
        'countryCode'  => 'SA',
        'tags'         => ['Website'],
      'custom_fields' => [
            [
                'name'  => 'contact_method',
                'value' => 'Website'
            ],
            [
                'name'  => 'user_type',
                'value' => 'Receiver'
            ],
            [
                'name'  => 'sender',
                'value' => $sender_name
            ],
            [
                'name'  => 'receiver',
                'value' => $receiver_name
            ]
        ]

    ];

    error_log('Sending request to Respond.io: ' . json_encode($body));

    $response = wp_remote_post($api_url, [
        'method'    => 'POST',
        'headers'   => [
            'Accept'        => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type'  => 'application/json',
        ],
        'body'      => json_encode($body),
        'timeout'   => 30
    ]);

    if (is_wp_error($response)) {
        error_log('Respond.io API Error: ' . $response->get_error_message());
    } else {
        error_log('Respond.io API Response: ' . wp_remote_retrieve_body($response));
    }
}


add_action('wpcf7_before_send_mail', 'process_contact_form_data_sender', 10, 1);

function process_contact_form_data_sender($contact_data) {
    $form_id = $contact_data->id();

    // Ensure only the form with ID 17971 triggers the API call
     if ($form_id != 18036 && $form_id != 18085) {
		return;
	}
    $submission = WPCF7_Submission::get_instance();
    if (!$submission) {
        error_log('Respond.io API Error: No submission data found.');
        return;
    }

    $form_data = $submission->get_posted_data();

    $receiver_name = sanitize_text_field($form_data['reciever-name'] ?? '');
    $phone = sanitize_text_field($form_data['phone2'] ?? '');
	$phone = preg_replace('/\s+/', '', $phone);
    $email         = sanitize_email($form_data['email-2'] ?? '');
    $city          = sanitize_text_field($form_data['city1'] ?? '');
    $relationship  = sanitize_text_field($form_data['relationship'] ?? '');

    $sender_name = sanitize_text_field($form_data['full-name'] ?? '');
    $phone1 = sanitize_text_field($form_data['phone1'] ?? '');
	$phone1 = preg_replace('/\s+/', '', $phone1);
    $email1         = sanitize_email($form_data['email-1'] ?? '');
    $city1          = sanitize_text_field($form_data['city'] ?? '');
    $store  = sanitize_text_field($form_data['store-name'] ?? '');

    if (empty($phone)) {
        error_log('Respond.io API Error: Phone number is required.');
        return;
    }

    $api_url = 'https://api.respond.io/v2/contact/phone:' . urlencode($phone1);
    $api_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTEyNzUsInNwYWNlSWQiOjI1MTQ5NCwib3JnSWQiOjI0OTAwNSwidHlwZSI6ImFwaSIsImlhdCI6MTczOTQzOTc1Nn0.ZeSeFfxDnPEPcvFXdGxeMcb2DeuYNCBa1xdqnoR8vHk';

    $body = [
        'firstName'    => $sender_name,
        'phone'        => $phone1,
        'email'        => $email1,
        'countryCode'  => 'SA',
        'tags'         => ['Website'],
      'custom_fields' => [
            [
                'name'  => 'contact_method',
                'value' => 'Website'
            ],
            [
                'name'  => 'user_type',
                'value' => 'Sender'
            ],
            [
                'name'  => 'sender',
                'value' => $sender_name
            ],
            [
                'name'  => 'receiver',
                'value' => $receiver_name
            ]
        ]

    ];

    error_log('Sending request to Respond.io: ' . json_encode($body));

    $response = wp_remote_post($api_url, [
        'method'    => 'POST',
        'headers'   => [
            'Accept'        => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type'  => 'application/json',
        ],
        'body'      => json_encode($body),
        'timeout'   => 30
    ]);

    if (is_wp_error($response)) {
        error_log('Respond.io API Error: ' . $response->get_error_message());
    } else {
        error_log('Respond.io API Response: ' . wp_remote_retrieve_body($response));
    }
}


add_action('wpcf7_before_send_mail', 'process_contact_form_sender', 10, 1);

function process_contact_form_sender($contact_form) {
	
	 $form_id = $contact_form->id();
	 
	if ($form_id != 18036 && $form_id != 18085) {
		return;
	}

    $submission = WPCF7_Submission::get_instance();

    if ($submission) {
        $posted_data = $submission->get_posted_data();

        // Get the form fields (adjust these field names based on your form setup)
        $name = isset($posted_data['full-name']) ? $posted_data['full-name'] : '';;
        $email = isset($posted_data['email-1']) ? $posted_data['email-1'] : '';
        $mob = isset($posted_data['phone2']) ? $posted_data['phone2'] : '';
 		$recievername = isset($posted_data['reciever-name']) ? $posted_data['reciever-name'] : '';
        // Prepare email details
        $to = $email; // Change this to the additional recipient
        $subject = 'Thanks for Registering with Hisense شكرا لتسجيلك مع هايسنس ';

        // HTML Email Body (Fixed Syntax Issues)
        $body = "
        <table border='0' cellpadding='0' cellspacing='0' width='100%' align='center' valign='top' style='padding:0px 40px 50px 40px; border-top: none;'>
            <tbody>
                <tr valign='top'>
                    <td style='padding: 20px 40px 20px 0;' colspan='2'>
                        <p style='font-family: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;color: #333333;font-size:18px;margin: 0;font-weight: 600;'>
                          Thanks for Registering with Hisense شكرا لتسجيلك مع هايسنس 
                        </p>
                    </td>
                </tr>
                <tr valign='top'>
                    <td style='color: #747474;font-size: 15px;font-family: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;padding:10px 0 10px 0px;' valign='top'>
                    <b>Ramadan Gifts with Hisense</b>
مع هايسنس الخير خيرين
مبروك, لقد قمت بتسجيل بياناتك و بيانات صديقك {$recievername} في مسابقة الخير خيرين من هايسنس لفرصة الفوز لك و لصديقك بجوائز قيمة من أجهزة هايسنس الذكية, تابع حساباتنا على وسائل التواصل الاجتماعي لترقب موعد إعلان الفائزين و عروضنا الرمضانية الحصرية 
Congratulations! You have successfully referred yourself and your friend {$recievername} for Hisense Ramadan campaign. Keep your fingers crossed for a chance to win amazing gifts after the campaign ends! <br><br>
Follow us on <a href='https://www.instagram.com/hisense_ksa' target='_blank'>Instagram</a>, <a href='https://twitter.com/Hisense_KSA' target='_blank'> X (Twitter)</a>, and <a href='https://www.tiktok.com/@hisense_ksa' target='_blank'>TikTok</a> to stay updated on the winner
نتمنى لك جمعات رمضانية مليئة بالبركة و السعادة لك ولأحبائك. حظاً موفقاً! 🌙✨
May this Ramadan bring you and your loved ones endless blessings and happiness. Good luck! 🌙✨

                    </td>
                
                </tr>
            </tbody>
        </table>";

        $headers = [
            "From: <EMAIL>", // Use a valid email instead of a phone number
            "Reply-To: $email", // Optional, or set this dynamically if available
            "Content-Type: text/html; charset=UTF-8"
        ];

        // Send the additional email
        wp_mail($to, $subject, $body, $headers);
    }
}

add_action('wpcf7_before_send_mail', 'process_contact_form_data_reciever', 10, 1);

function process_contact_form_data_reciever($contact_form) {
	$form_id = $contact_form->id();
	 if ($form_id != 18036 && $form_id != 18085) {
		return;
	}
	
    $submission = WPCF7_Submission::get_instance();

    if ($submission) {
        $posted_data = $submission->get_posted_data();

        // Get the form fields (adjust these field names based on your form setup)
        $senderName = isset($posted_data['full-name']) ? $posted_data['full-name'] : '';
		    $name1 = isset($posted_data['reciever-name']) ? $posted_data['reciever-name'] : '';
        $email1 = isset($posted_data['email-2']) ? $posted_data['email-2'] : '';
        $phone = isset($posted_data['phone1']) ? $posted_data['phone1'] : '';


        // Prepare email details
        $to = $email1; // Change this to the additional recipient
        $subject = 'هديتك من هايسنس في رمضان  Your Ramadan Gift from Hisense ';

        // HTML Email Body (Fixed Syntax Issues)
        $body = "
        <table border='0' cellpadding='0' cellspacing='0' width='100%' align='center' valign='top' style='padding:0px 40px 50px 40px; border-top: none;'>
            <tbody>
                <tr valign='top'>
                    <td style='padding: 20px 40px 20px 0;' colspan='2'>
                        <p style='font-family: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;color: #333333;font-size:18px;margin: 0;font-weight: 600;'>
                        هديتك من هايسنس في رمضان  Your Ramadan Gift from Hisense 
                        </p>
                    </td>
                </tr>


                <tr valign='top'>
                    <td style='color: #747474;font-size: 15px;font-family: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;padding:10px 0 10px 0px;' valign='top'>
                       <b>Ramadan Gifts with Hisense </b>
مع هايسنس الخير خيرين
لأن رمضان شهر الكرم. صديقك {$senderName} سجل بياناتك لتفوز معه بإحدى جوائز هايسنس في شهر رمضان الكريم, ما تفوت الفرصة لزيادة فرصة فوزك بتسجيل بياناتك و بيانات من تحب ان يفوز معك. مع هايسنس الخير خيري
Great news! Your friend {$senderName} has referred you to the Hisense Ramadan campaign. Don’t miss your chance to win fantastic gifts in the lucky draw! Start spreading the joy by referring to your loved ones now.<br><br>
<a href='https://www.hisenseksa.com/ramadan-with-hisense/'>Register NOW </a> <a href='https://www.hisenseksa.com/ramadan-with-hisense/'>سجل الآن </a>   <br><br>
تابع حساباتنا على وسائل التواصل الاجتماعي لترقب موعد إعلان الفائزين و عروضنا الرمضانية الحصرية
Follow us on <a href='https://www.instagram.com/hisense_ksa' target='_blank'>Instagram</a>, <a href='https://twitter.com/Hisense_KSA' target='_blank'> X (Twitter)</a>, and <a href='https://www.tiktok.com/@hisense_ksa' target='_blank'>TikTok</a> to stay updated on the winner <br><br>
نتمنى لك جمعات رمضانية مليئة بالبركة و السعادة لك ولأحبائك. حظاً موفقاً! 🌙✨
May this Ramadan bring you and your loved ones endless blessings and happiness. Good luck! 🌙✨

	

                    </td>
                
                </tr>
            </tbody>
        </table>";

        $headers = [
            "From: <EMAIL>", // Use a valid email instead of a phone number
            "Reply-To: $email1", // Optional, or set this dynamically if available
            "Content-Type: text/html; charset=UTF-8"
        ];

        // Send the additional email
        wp_mail($to, $subject, $body, $headers);
    }
}


function hisense_enqueue_template_styles() {
    if (is_page_template('template-thankyou-ramada.php')) { // Ensure the filename matches your template
        wp_enqueue_style('fridge-overview-style', get_template_directory_uri() . '/assets/css/fridge-overview.css', [], '1.8', 'all');
    }
}
add_action('wp_enqueue_scripts', 'hisense_enqueue_template_styles');




// wc

// Force 4 products per page on shop and category pages
// Force 4 products per page on shop and category pages
function set_products_per_page($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_shop() || is_product_category() || is_product_tag()) {
            $query->set('posts_per_page', 4);
        }
    }
}
add_action('pre_get_posts', 'set_products_per_page');

// Set WooCommerce loop property for products per page
function wc_set_loop_products_per_page() {
    if (is_shop() || is_product_category() || is_product_tag()) {
        wc_set_loop_prop('per_page', 4);
    }
}
add_action('woocommerce_before_shop_loop', 'wc_set_loop_products_per_page');

// Fix WooCommerce pagination calculation
function fix_woocommerce_pagination() {
    if (is_shop() || is_product_category() || is_product_tag()) {
        global $wp_query;

        // Force recalculate pagination based on 4 products per page
        if (isset($wp_query->found_posts)) {
            $wp_query->max_num_pages = ceil($wp_query->found_posts / 4);

            // Set WooCommerce loop properties
            wc_set_loop_prop('total_pages', $wp_query->max_num_pages);
            wc_set_loop_prop('per_page', 4);
            wc_set_loop_prop('total', $wp_query->found_posts);

            // Make sure current page is set correctly
            $current_page = max(1, get_query_var('paged'));
            wc_set_loop_prop('current_page', $current_page);
        }
    }
}
add_action('wp', 'fix_woocommerce_pagination');

// Additional fix for WooCommerce loop properties
function fix_wc_loop_props() {
    if (is_shop() || is_product_category() || is_product_tag()) {
        global $wp_query;

        // Override any incorrect pagination calculations
        if (isset($wp_query->found_posts)) {
            $correct_pages = ceil($wp_query->found_posts / 4);

            // Force correct pagination in WooCommerce
            add_filter('woocommerce_pagination_args', function($args) use ($correct_pages) {
                $args['total'] = $correct_pages;
                return $args;
            });
        }
    }
}
add_action('woocommerce_before_shop_loop', 'fix_wc_loop_props');


// add_action('woocommerce_checkout_order_processed', function ($order_id, $posted_data, $order) {
//     if ($order && $order->get_status() === 'processing') {
//         WC()->mailer()->emails['WC_Email_Customer_Processing_Order']->trigger($order_id);
//     }
// }, 10, 3);

// add_action('woocommerce_order_status_changed', function ($order_id, $from, $to, $order) {
//     if ($to === 'processing') {
//         WC()->mailer()->emails['WC_Email_Customer_Processing_Order']->trigger($order_id);
//     }
// }, 10, 4);

// WCFM CSV Import functionality has been moved to the WCFM CSV Import Enhancer plugin
// Please activate the plugin located at: wp-content/plugins/wcfm-csv-import-enhancer/

add_action('init', function () {
    $order_id = 20444; // Use numeric order ID only (WB prefix is display only)

    // Step 1: Rebuild vendor data in WCFM
    do_action('wcfm_order_processed', $order_id);

    // Step 2: Trigger vendor email (WCFM Free)
    do_action('wcfm_after_order_status_update', $order_id, 'processing');

    // Step 3: If you're using WCFM Marketplace, trigger additional notification
    if (function_exists('wcfmmp_is_marketplace')) {
        do_action('wcfmmp_order_status_update_notification', $order_id, 'processing');
    }

    // Optional: Run only once to avoid repeat emails
    // remove_action or delete this hook after running once
});




// Simple Checkout.com email fix
add_action('woocommerce_order_status_changed', function($order_id, $from, $to, $order) {
    // Only for Checkout.com orders
    if (strpos($order->get_payment_method(), 'wc_checkout_com_') === false) {
        return;
    }
    
    // Only for processing status
    if ($to !== 'processing') {
        return;
    }
    
    // Force send emails
    $mailer = WC()->mailer();
    $emails = $mailer->get_emails();
    
    if (isset($emails['WC_Email_Customer_Processing_Order'])) {
        $emails['WC_Email_Customer_Processing_Order']->trigger($order_id);
    }
    
    if (isset($emails['WC_Email_New_Order'])) {
        $emails['WC_Email_New_Order']->trigger($order_id);
    }
}, 10, 4);


// *********************************

function handle_worldcup_registration() {
    if ( isset( $_POST['register'] ) ) {
        // Sanitize and validate input fields
        $first_name = sanitize_text_field( $_POST['billing_first_name'] );
        $last_name = sanitize_text_field( $_POST['billing_last_name'] );
        $email = sanitize_email( $_POST['email'] );
        $phone = sanitize_text_field( $_POST['billing_phone'] );
        $city = sanitize_text_field( $_POST['billing_city'] ); // Use the new name
        $password = sanitize_text_field( $_POST['password'] );
        $password2 = sanitize_text_field( $_POST['password2'] );
        $terms = isset( $_POST['terms'] );

        $errors = new WP_Error();

        // Validation checks
        if ( empty( $first_name ) ) {
            $errors->add( 'first_name_error', __( 'Please enter your first name.', 'your-theme' ) );
        }
        if ( empty( $last_name ) ) {
            $errors->add( 'last_name_error', __( 'Please enter your last name.', 'your-theme' ) );
        }
        if ( ! is_email( $email ) ) {
            $errors->add( 'email_error', __( 'Please enter a valid email address.', 'your-theme' ) );
        }
        if ( email_exists( $email ) ) {
            $errors->add( 'email_exists_error', __( 'This email address is already registered.', 'your-theme' ) );
        }
        if ( empty( $phone ) ) {
            $errors->add( 'phone_error', __( 'Please enter your phone number.', 'your-theme' ) );
        }
        if ( empty( $city ) ) {
            $errors->add( 'city_error', __( 'Please select your city.', 'your-theme' ) );
        }
        if ( strlen( $password ) < 6 ) {
            $errors->add( 'password_length_error', __( 'Password must be at least 6 characters long.', 'your-theme' ) );
        }
        if ( $password !== $password2 ) {
            $errors->add( 'password_mismatch_error', __( 'Passwords do not match.', 'your-theme' ) );
        }
        if ( ! $terms ) {
            $errors->add( 'terms_error', __( 'You must agree to the terms and conditions and privacy policy.', 'your-theme' ) );
        }

        if ( ! $errors->has_errors() ) {
            $username = sanitize_user( $email, true ); // Use email as username or generate one
            $user_id = wp_create_user( $username, $password, $email );

            if ( ! is_wp_error( $user_id ) ) {
                // Additional user meta
                update_user_meta( $user_id, 'first_name', $first_name );
                update_user_meta( $user_id, 'last_name', $last_name );
                update_user_meta( $user_id, 'billing_phone', $phone );
                update_user_meta( $user_id, 'billing_city', $city ); // Save the city

                // Log the user in
                wp_set_auth_cookie( $user_id, true );
                wp_redirect( home_url( '/goal-prediction-fcwc/predictions/' ) ); // Redirect to a thank you page
                exit;
            } else {
                $errors = $user_id; // Pass WP_Error from user creation
            }
        }

        // Display errors in your template
         global $registration_errors;
    	$registration_errors = $errors;
    }
}
add_action( 'template_redirect', 'handle_worldcup_registration' );





// login

function handle_worldcup_login() {
    if ( isset( $_POST['login'] ) ) { // Check if the login form was submitted
        $username = sanitize_text_field( $_POST['username'] );
        $password = sanitize_text_field( $_POST['password'] );
        $remember = true; // You can add a checkbox for "Remember Me" if you want

        $user = wp_signon( array(
            'user_login'    => $username,
            'user_password' => $password,
            'remember'      => $remember,
        ), false );

        if ( is_wp_error( $user ) ) {
            // Store the error message in a session variable
            session_start();
            $_SESSION['login_error'] = $user->get_error_message();
            session_write_close(); // Close session before redirect to prevent REST API interference
            // Redirect back to the login page to display the error
            wp_redirect( get_permalink() ); // Redirect to the current page (login page)
            exit;
        } else {
            // Successful login, redirect the user to the predictor page
            wp_redirect( home_url( '/goal-prediction-fcwc/predictions/' ) );
            exit;
        }
    }
}
add_action( 'template_redirect', 'handle_worldcup_login' );

/**
 * Display login error message on the login page.
 */
function display_login_error() {
    session_start();
    if ( isset( $_SESSION['login_error'] ) ) {
        echo '<div class="error-message">' . $_SESSION['login_error'] . '</div>';
        unset( $_SESSION['login_error'] ); // Clear the error message after displaying it
    }
    session_write_close(); // Close session to prevent REST API interference
}




/**
 * Handle user profile updates (picture and name).
 */
function handle_user_profile_updates() {
    if ( ! is_user_logged_in() ) {
        return; // Only allow logged-in users to update their profile.
    }

    $current_user_id = get_current_user_id();
    // Use the current URL as the base for redirection to keep the user on the profile page.
    $redirect_url = get_permalink( get_the_ID() );
    if ( ! $redirect_url ) { // Fallback if get_permalink fails (e.g., not within a loop)
        $redirect_url = home_url('/profile/'); // Replace '/profile/' with your actual profile page slug
    }

    // Initialize update status to 'no_changes' and override if any update occurs.
    $final_update_status = 'no_changes';

    // Check if the form was submitted
    if ( isset( $_POST['submit_profile_updates'] ) ) {

        // Verify nonce for security FIRST
        if ( ! isset( $_POST['profile_update_nonce_field'] ) || ! wp_verify_nonce( $_POST['profile_update_nonce_field'], 'update_user_profile' ) ) {
            wp_redirect( add_query_arg( 'update_status', 'fail_security', $redirect_url ) );
            exit; // Exit immediately on security failure
        }

        $update_occurred = false; // Flag to track if any part of the profile was actually updated

        // --- Handle Name Updates ---
        $first_name = isset( $_POST['first_name'] ) ? sanitize_text_field( wp_unslash( $_POST['first_name'] ) ) : '';
        $last_name = isset( $_POST['last_name'] ) ? sanitize_text_field( wp_unslash( $_POST['last_name'] ) ) : '';

        $current_first_name = get_user_meta($current_user_id, 'first_name', true);
        $current_last_name = get_user_meta($current_user_id, 'last_name', true);

        if ( $first_name !== $current_first_name ) {
            update_user_meta( $current_user_id, 'first_name', $first_name );
            $update_occurred = true;
        }
        if ( $last_name !== $current_last_name ) {
            update_user_meta( $current_user_id, 'last_name', $last_name );
            $update_occurred = true;
        }

        // Update display name if it changed or if first/last name changed
        $user_obj = get_user_by( 'id', $current_user_id );
        $new_display_name = trim( $first_name . ' ' . $last_name );
        if ( empty( $new_display_name ) ) {
            $new_display_name = $user_obj->user_login; // Fallback to username
        }

        if ( $new_display_name !== $user_obj->display_name ) {
             wp_update_user( array(
                'ID'            => $current_user_id,
                'display_name'  => $new_display_name,
            ) );
            $update_occurred = true;
        }


        // --- Handle Profile Picture Update/Deletion ---

        // Flag to check if a file was uploaded
        $file_uploaded = ( isset( $_FILES['profile_picture'] ) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK );
        // Flag to check if the delete checkbox was checked
        $delete_picture_checked = ( isset( $_POST['delete_profile_picture'] ) && $_POST['delete_profile_picture'] === '1' );

        // Scenario 1: User wants to upload a new picture
        if ( $file_uploaded ) {
            $file = $_FILES['profile_picture'];

            $file_name = sanitize_file_name( $file['name'] );
            $file_type = wp_check_filetype( $file_name, null );

            if ( ! in_array( $file_type['ext'], array( 'jpg', 'jpeg', 'png', 'gif' ) ) ) {
                $final_update_status = 'invalid_file'; // Set specific status for invalid file
            } else {
                require_once( ABSPATH . 'wp-admin/includes/image.php' );
                require_once( ABSPATH . 'wp-admin/includes/file.php' );
                require_once( ABSPATH . 'wp-admin/includes/media.php' );

                $uploaded_file = wp_handle_upload( $file, array( 'test_form' => false ) );

                if ( isset( $uploaded_file['file'] ) ) {
                    // Delete old picture if it exists before saving new one
                    $old_profile_picture_url = get_user_meta( $current_user_id, 'profile_image', true );
                    if ( ! empty( $old_profile_picture_url ) ) {
                        $upload_dir = wp_upload_dir();
                        $base_dir = $upload_dir['basedir'];
                        $old_file_path = str_replace( $upload_dir['baseurl'], $base_dir, $old_profile_picture_url );
                        if ( file_exists( $old_file_path ) ) {
                            unlink( $old_file_path );
                        }
                    }

                    $file_url = $uploaded_file['url'];
                    update_user_meta( $current_user_id, 'profile_image', esc_url_raw( $file_url ) );
                    $update_occurred = true;
                    $final_update_status = 'success'; // Tentatively set success
                } else {
                    $final_update_status = 'fail_upload'; // Set specific status for upload failure
                }
            }
        }
        // Scenario 2: User wants to delete the existing picture (only if no new file uploaded)
        elseif ( $delete_picture_checked ) {
            $profile_picture_url = get_user_meta( $current_user_id, 'profile_image', true );

            if ( ! empty( $profile_picture_url ) ) {
                $upload_dir = wp_upload_dir();
                $base_dir = $upload_dir['basedir'];
                $file_path = str_replace( $upload_dir['baseurl'], $base_dir, $profile_picture_url );

                if ( file_exists( $file_path ) ) {
                    unlink( $file_path );
                }
                delete_user_meta( $current_user_id, 'profile_image' );
                $update_occurred = true;
                $final_update_status = 'success'; // Tentatively set success
            } else {
                // No picture to delete, but checkbox was checked. Could indicate 'no changes'.
                // Do nothing or handle as a 'no_changes'
            }
        }

        // Final redirection based on overall updates
        if ( $update_occurred && $final_update_status !== 'fail_upload' && $final_update_status !== 'invalid_file' ) {
            $final_update_status = 'success';
        }

        wp_redirect( add_query_arg( 'update_status', $final_update_status, $redirect_url ) );
        exit;
    }
}
add_action( 'template_redirect', 'handle_user_profile_updates' );


add_action('wp_ajax_get_user_full_name', 'get_user_full_name_callback');
add_action('wp_ajax_nopriv_get_user_full_name', 'get_user_full_name_callback');

function get_user_full_name_callback() {
    $email = sanitize_email($_POST['email'] ?? '');

    if (!is_email($email)) {
        wp_send_json_error(['message' => 'Invalid email']);
    }

    $user = get_user_by('email', $email);

    if ($user) {
        $first_name = get_user_meta($user->ID, 'first_name', true);
        $last_name = get_user_meta($user->ID, 'last_name', true);
        $full_name = trim($first_name . ' ' . $last_name);
        if (empty($full_name)) {
            $full_name = $user->display_name;
        }
        wp_send_json_success(['full_name' => $full_name]);
    } else {
        wp_send_json_error(['message' => 'User not found']);
    }
}



// In your theme's functions.php

function custom_scorecard_rewrite_rule() {
    add_rewrite_rule(
        '^scorecard/([0-9]+)/?$',
        'index.php?pagename=scorecard&user_id=$matches[1]',
        'top'
    );
}
add_action('init', 'custom_scorecard_rewrite_rule');

function custom_scorecard_query_vars($vars) {
    $vars[] = 'user_id';
    return $vars;
}
add_filter('query_vars', 'custom_scorecard_query_vars');

// Flush rewrite rules on theme activation/update
function custom_scorecard_flush_rewrites() {
    custom_scorecard_rewrite_rule();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'custom_scorecard_flush_rewrites');


function custom_profile_rewrite_rule() {
    add_rewrite_rule(
        '^prediction-profile/([0-9]+)/?$',
        'index.php?pagename=prediction-profile&user_id=$matches[1]',
        'top'
    );
}
add_action('init', 'custom_profile_rewrite_rule');

function custom_profile_query_vars($vars) {
    $vars[] = 'user_id';
    return $vars;
}
add_filter('query_vars', 'custom_profile_query_vars');

function custom_profile_flush_rewrites() {
    custom_profile_rewrite_rule();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'custom_profile_flush_rewrites');


// Redirect login failure back to my-account page with errors shown
add_filter('woocommerce_login_failed_redirect', function($redirect) {
    return wc_get_page_permalink('myaccount');
});

// Redirect after login success to dashboard or my-account
add_filter('woocommerce_login_redirect', function($redirect, $user) {
    return wc_get_page_permalink('myaccount');
}, 10, 2);

// Hook after user is created to set password from registration form
add_action('woocommerce_created_customer', 'custom_set_user_password_from_registration', 10, 1);

function custom_set_user_password_from_registration($customer_id) {
    if ( isset($_POST['password']) && !empty($_POST['password']) ) {
        $password = sanitize_text_field($_POST['password']);
        wp_set_password($password, $customer_id);
    }
}

add_action('template_redirect', function () {
    if (
        is_user_logged_in() &&
        is_account_page()
    ) {
        $current_path = untrailingslashit(wp_parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
        $account_path = untrailingslashit(wp_parse_url(wc_get_page_permalink('myaccount'), PHP_URL_PATH));

        if ($current_path === $account_path) {
            wp_redirect(wc_get_account_endpoint_url('edit-account'));
            exit;
        }
    }
});

add_filter('woocommerce_add_to_cart_fragments', 'custom_header_cart_fragment');

function custom_header_cart_fragment($fragments) {
	ob_start();

	// Load your mini-cart file from the WooCommerce override path
	wc_get_template('cart/mini-cart.php');

	$fragments['.mini-cart'] = ob_get_clean();
	return $fragments;
}



/**
 * Limit WooCommerce checkout and billing country to Saudi Arabia.
 */
// add_filter( 'woocommerce_countries_allowed_countries', 'limit_woocommerce_country_to_saudi_arabia' );

// function limit_woocommerce_country_to_saudi_arabia( $countries ) {
//     // KSA is the country code for Saudi Arabia
//     $allowed_countries = array( 'SA' => $countries['SA'] );
//     return $allowed_countries;
// }

// add_filter( 'woocommerce_shipping_countries', 'limit_woocommerce_country_to_saudi_arabia' );




/**
 * Force WooCommerce country field to be a dropdown with only Saudi Arabia.
 */
add_filter( 'woocommerce_checkout_fields', 'force_saudi_arabia_country_dropdown' );

function force_saudi_arabia_country_dropdown( $fields ) {
    // Get the country data from WooCommerce
    $countries_obj = new WC_Countries();
    $countries     = $countries_obj->get_countries();

    // Get the label for Saudi Arabia (ensuring it's localized)
    $saudi_arabia_label = isset( $countries['SA'] ) ? $countries['SA'] : 'Saudi Arabia';

    // Override the billing country field
    $fields['billing']['billing_country'] = array(
        'type'        => 'select', // <--- THIS FORCES THE <select> ELEMENT
        'label'       => __( 'Country', 'woocommerce' ),
        'options'     => array(
			''   => __( 'Select a country / region', 'woocommerce' ),
            'SA' => $saudi_arabia_label, // <--- ONLY 'SA' IS ADDED AS AN OPTION
        ),
        'default'     => 'SA', // Set Saudi Arabia as the default selected option
        'class'       => array( 'form-row-wide', 'woocommerce-country-dropdown' ), // Add some classes
        'required'    => true,
        'clear'       => true,
        'priority'    => 40, // Adjust priority if needed to control order
    );

    // If shipping is enabled and different from billing, also override shipping country field
    if ( WC()->cart->needs_shipping_address() ) {
        $fields['shipping']['shipping_country'] = array(
            'type'        => 'select',
            'label'       => __( 'Country', 'woocommerce' ),
            'options'     => array(
				''   => __( 'Select a country / region', 'woocommerce' ),
                'SA' => $saudi_arabia_label,
            ),
            'default'     => 'SA',
            'class'       => array( 'form-row-wide', 'woocommerce-country-dropdown' ),
            'required'    => true,
            'clear'       => true,
            'priority'    => 40,
        );
    }

    return $fields;
}

// This filter makes sure WooCommerce internally recognizes only 'SA' as allowed,
// even though we're manually building the dropdown. This should be kept alongside the above.
// add_filter( 'woocommerce_countries_allowed_countries', 'set_only_saudi_arabia_allowed' );
// add_filter( 'woocommerce_shipping_countries', 'set_only_saudi_arabia_allowed' );

// function set_only_saudi_arabia_allowed( $countries ) {
//     return array( 'SA' => $countries['SA'] );
// }

add_filter( 'woocommerce_default_address_fields', 'force_saudi_arabia_in_account_address' );

function force_saudi_arabia_in_account_address( $fields ) {
    $countries_obj = new WC_Countries();
    $countries     = $countries_obj->get_countries();
    $saudi_arabia_label = isset( $countries['SA'] ) ? $countries['SA'] : 'Saudi Arabia';

    $fields['country']['type']    = 'select';
    $fields['country']['options'] = array(
        ''   => __( 'Select a country / region', 'woocommerce' ),
        'SA' => $saudi_arabia_label,
    );
    $fields['country']['default'] = '';

    return $fields;
}
add_filter( 'woocommerce_default_address_fields', 'remove_state_from_account_address' );

function remove_state_from_account_address( $fields ) {
    unset( $fields['state'] );
    return $fields;
}
add_filter( 'woocommerce_billing_fields', 'remove_billing_address_2_field_from_account', 20 );

function remove_billing_address_2_field_from_account( $fields ) {
    unset( $fields['billing_address_2'] );
    return $fields;
}
// add class
add_action( 'wp_footer', 'custom_add_address_button_class_script' );
function custom_add_address_button_class_script() {
    if ( is_account_page() ) {
        ?>
        <script>
        jQuery(document).ready(function($) {
            $('.fabfw-add-address-container button[type="submit"]').addClass('woo-btn-style');
        });
        </script>
        <?php
    }
}



add_action('wp_footer', 'add_checkout_custom_span_class_script');

function add_checkout_custom_span_class_script() {
    // Only load on checkout page
    if (is_checkout()) {
        ?>
        <script type="text/javascript">
        jQuery(function ($) {
            // Add class to billing country wrapper
            $('#billing_country_field .woocommerce-input-wrapper').addClass('custom-select-class');

            // Optional: If you also use shipping fields
            $('#shipping_country_field .woocommerce-input-wrapper').addClass('custom-select-class');
        });
        </script>
        <?php
    }
}


// prediction export

// Enqueue the JavaScript file only on the target admin page
function wcup_enqueue_button_script( $hook_suffix ) {
    // Check if we are on the 'wcup_predictions' admin page
    if ( isset( $_GET['page'] ) && $_GET['page'] === 'wcup_predictions' ) {
        // Enqueue jQuery (WordPress loads it by default in admin)
        wp_enqueue_script( 'jquery' );

        // Enqueue your custom script
        wp_enqueue_script(
            'wcup-custom-button-script',
            plugin_dir_url( __FILE__ ) . 'wcup-custom-button.js', // Path to your JS file
            array( 'jquery' ), // Depends on jQuery
            '1.0',
            true // Load in footer
        );

        // Optionally, localize a variable if you need to pass data to JS
        // wp_localize_script( 'wcup-custom-button-script', 'wcupButtonData', array(
        //     'buttonHtml' => '<button id="my-dynamic-button" class="button button-primary">Dynamic Button After Form</button>',
        // ) );
    }
}
add_action( 'admin_enqueue_scripts', 'wcup_enqueue_button_script' );

// Output the button HTML in a hidden div, to be moved by JavaScript
// We use a general hook like 'admin_footer' because its content will be moved

function wcup_output_button_html_for_js() {
    // Check if we are on the specific admin page 'wcup_predictions'
    if ( isset( $_GET['page'] ) && $_GET['page'] === 'wcup_predictions' ) {
        ?>
        <div id="wcup-button-container" style="max-width:95%; text-align:right; margin-bottom:50px; ">
            <button id="export-all-predictions-csv" class="button button-primary">Export All Predictions CSV</button>
        </div>

        <script type="text/javascript">
            // Wait for the DOM to be fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                const exportButton = document.getElementById('export-all-predictions-csv');

                if (exportButton) {
                    exportButton.addEventListener('click', function() {
                        // Construct the URL for the CSV download
                        // This will trigger the hisense_worldcup_download_all_predictions_csv function
                        const downloadUrl = '<?php echo esc_url( admin_url( '?download_all_predictions_csv=1' ) ); ?>';
                        window.location.href = downloadUrl;
                    });
                }
            });
        </script>
        <?php
    }
}
add_action( 'admin_footer', 'wcup_output_button_html_for_js' ); // Use admin_footer to ensure button and script load after page content




/**
 * Handles the CSV download for all World Cup user predictions.
 *
 * This function checks for a specific 'download_all_predictions_csv' query parameter
 * to generate and serve a CSV file containing predictions from all users.
 * This should ideally be accessible only to administrators.
 */
function hisense_worldcup_download_all_predictions_csv() {
    // Only proceed if the 'download_all_predictions_csv' parameter is set and is '1'
    // and if the current user has the capability to manage options (e.g., Administrator).
    if (isset($_GET['download_all_predictions_csv']) && $_GET['download_all_predictions_csv'] == '1') {
        if (!current_user_can('manage_options')) {
            wp_die('You do not have sufficient permissions to access this page.');
        }

        global $wpdb;

        $predictions_table = $wpdb->prefix . 'wcup_prediction';
        $matches_table = $wpdb->prefix . 'wcup_match';
        $teams_table = $wpdb->prefix . 'wcup_team';
        $users_table = $wpdb->prefix . 'users'; // WordPress users table

        $all_predictions_for_csv = $wpdb->get_results(
            "SELECT
                p.user_id,
                u.user_login,
                u.display_name,
                u.user_email, -- Added user_email field
                p.match_id,
                p.home_goals AS user_home_goals,
                p.away_goals AS user_away_goals,
                p.home_penalties AS user_home_penalties,
                p.away_penalties AS user_away_penalties,
                p.points,
                m.kickoff,
                m.home_goals AS actual_home_goals,
                m.away_goals AS actual_away_goals,
                m.home_penalties AS actual_home_penalties,
                m.away_penalties AS actual_away_penalties,
                m.is_result,
                m.extra_time,
                ht.name AS home_team_name,
                at.name AS away_team_name
            FROM
                {$predictions_table} AS p
            INNER JOIN
                {$matches_table} AS m ON p.match_id = m.match_id
            LEFT JOIN
                {$teams_table} AS ht ON m.home_team_id = ht.team_id
            LEFT JOIN
                {$teams_table} AS at ON m.away_team_id = at.team_id
            INNER JOIN
                {$users_table} AS u ON p.user_id = u.ID
            ORDER BY
                u.user_login ASC, m.kickoff ASC", // Order by user, then by kickoff time
            ARRAY_A // Get results as an associative array for easier CSV handling
        );

        if ($all_predictions_for_csv) {
            $filename = 'all_user_predictions_' . date('Ymd_His') . '.csv';

            // Set headers for CSV download
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Pragma: no-cache');
            header('Expires: 0');

            $output = fopen('php://output', 'w');
 fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); 
            // CSV Header
            $header_row = [
                'User ID',
                'Username',
                'Display Name',
                'Email', // Added Email header
                'Match ID',
                'Home Team',
                'Away Team',
                'Kickoff Date/Time',
                'User Predicted Home Goals',
                'User Predicted Away Goals',
                'User Predicted Home Penalties',
                'User Predicted Away Penalties',
                'Actual Home Goals',
                'Actual Away Goals',
                'Actual Home Penalties',
                'Actual Away Penalties',
                'Match Result Available',
                'Extra Time Played',
                'Points Earned'
            ];
            fputcsv($output, $header_row);
             // Add UTF-8 BOM (Byte Order Mark) to the beginning of the file
            // This is crucial for Excel to correctly interpret UTF-8 characters.
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

            // CSV Data
            foreach ($all_predictions_for_csv as $row) {
                // Fetch first and last names from user meta for display name if available
                $first_name = get_user_meta($row['user_id'], 'first_name', true);
                $last_name = get_user_meta($row['user_id'], 'last_name', true);
                $display_name = trim($first_name . ' ' . $last_name);
                if (empty($display_name)) {
                    $display_name = $row['display_name']; // Fallback to user_display_name if first/last are empty
                }

                $data_row = [
                    $row['user_id'],
                    $row['user_login'],
                    $display_name,
                    $row['user_email'], // Added user_email to data row
                    $row['match_id'],
                    $row['home_team_name'],
                    $row['away_team_name'],
                    $row['kickoff'],
                    $row['user_home_goals'],
                    $row['user_away_goals'],
                    $row['user_home_penalties'],
                    $row['user_away_penalties'],
                    ($row['is_result'] ? $row['actual_home_goals'] : 'N/A'),
                    ($row['is_result'] ? $row['actual_away_goals'] : 'N/A'),
                    ($row['is_result'] ? $row['actual_home_penalties'] : 'N/A'),
                    ($row['is_result'] ? $row['actual_away_penalties'] : 'N/A'),
                    ($row['is_result'] ? 'Yes' : 'No'),
                    ($row['extra_time'] ? 'Yes' : 'No'),
                    $row['points']
                ];
                fputcsv($output, $data_row);
            }

            fclose($output);
            exit; // Crucial: Stop script execution after serving the CSV
        } else {
            // If no predictions found, you might want to redirect with a message
            wp_redirect(admin_url('?csv_status=no_data')); // Redirect to admin dashboard with a status
            exit;
        }
    }
}
add_action('init', 'hisense_worldcup_download_all_predictions_csv');

// hide social login from wp-admin
function custom_login_styles() {
    echo '<style>
      body.login.js.login-action-login.wp-core-ui div#nsl-custom-login-form-main {
		display: none !important;
	}
    </style>';
}
add_action('login_head', 'custom_login_styles');


// Override WooCommerce placeholder image URL
add_filter('woocommerce_placeholder_img_src', 'hisense_custom_placeholder_image');
function hisense_custom_placeholder_image($src) {
    return 'https://www.hisenseksa.com/wp-content/uploads/2025/06/new-dummy.svg';
}

// Override WooCommerce placeholder <img> output
add_filter('woocommerce_placeholder_img', 'hisense_custom_placeholder_img');
function hisense_custom_placeholder_img($html) {
    $image_url = 'https://www.hisenseksa.com/wp-content/uploads/2025/06/new-dummy.svg';
    return '<img src="' . esc_url($image_url) . '" alt="' . esc_attr__('No image available', 'woocommerce') . '" class="woocommerce-placeholder wp-post-image" />';
}

// Improve search functionality with better product matching
add_filter('posts_search', 'improve_product_search', 10, 2);
function improve_product_search($search, $query) {
    if (!is_search() || is_admin()) {
        return $search;
    }

    global $wpdb;
    $search_term = $query->get('s');

    if (empty($search_term)) {
        return $search;
    }

    // Enhanced search that includes product meta fields, SKU, and categories
    $search = " AND (
        ({$wpdb->posts}.post_title LIKE '%{$search_term}%')
        OR ({$wpdb->posts}.post_content LIKE '%{$search_term}%')
        OR ({$wpdb->posts}.post_excerpt LIKE '%{$search_term}%')
        OR EXISTS (
            SELECT 1 FROM {$wpdb->postmeta}
            WHERE {$wpdb->postmeta}.post_id = {$wpdb->posts}.ID
            AND (
                ({$wpdb->postmeta}.meta_key = '_sku' AND {$wpdb->postmeta}.meta_value LIKE '%{$search_term}%')
                OR ({$wpdb->postmeta}.meta_key LIKE '%product%' AND {$wpdb->postmeta}.meta_value LIKE '%{$search_term}%')
            )
        )
        OR EXISTS (
            SELECT 1 FROM {$wpdb->term_relationships} tr
            INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            INNER JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
            WHERE tr.object_id = {$wpdb->posts}.ID
            AND t.name LIKE '%{$search_term}%'
        )
    )";

    return $search;
}

// Add search result highlighting
add_filter('the_title', 'highlight_search_terms_in_title', 10, 2);
function highlight_search_terms_in_title($title, $post_id = null) {
    if (is_search() && !is_admin()) {
        $search_term = get_search_query();
        if (!empty($search_term)) {
            $title = preg_replace('/(' . preg_quote($search_term, '/') . ')/i', '<mark>$1</mark>', $title);
        }
    }
    return $title;
}

// Add search result highlighting for content
add_filter('the_excerpt', 'highlight_search_terms_in_excerpt');
function highlight_search_terms_in_excerpt($excerpt) {
    if (is_search() && !is_admin()) {
        $search_term = get_search_query();
        if (!empty($search_term)) {
            $excerpt = preg_replace('/(' . preg_quote($search_term, '/') . ')/i', '<mark>$1</mark>', $excerpt);
        }
    }
    return $excerpt;
}

// Add search highlighting styles
add_action('wp_head', 'search_highlighting_styles');
function search_highlighting_styles() {
    if (is_search()) {
        ?>
        <style>
        mark {
            background-color: #ffeb3b;
            color: #333;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .search-results-page mark {
            background-color: #e74c3c;
            color: white;
        }
        </style>
        <?php
    }
}

// Force cart and checkout to display prices excluding tax
add_action('init', 'force_cart_display_prices_excluding_tax');
function force_cart_display_prices_excluding_tax() {
    // Ensure cart displays prices excluding tax
    add_filter('woocommerce_cart_display_prices_including_tax', '__return_false');
    add_filter('woocommerce_checkout_display_prices_including_tax', '__return_false');
}

// Custom subtotal display for cart and checkout (excluding tax)
add_filter('woocommerce_cart_subtotal', 'custom_cart_subtotal_excluding_tax', 10, 3);
function custom_cart_subtotal_excluding_tax($cart_subtotal, $compound, $cart) {
    // Get subtotal excluding tax
    $subtotal_excl_tax = $cart->get_subtotal();

    // Format the price
    $formatted_subtotal = wc_price($subtotal_excl_tax);

    // Add "excl. VAT" text
    $formatted_subtotal .= '<br><small>(excl. VAT)</small>';

    return $formatted_subtotal;
}

// Ensure tax is displayed separately and clearly
add_action('woocommerce_cart_totals_after_order_total', 'display_tax_total_separately');
add_action('woocommerce_review_order_after_order_total', 'display_tax_total_separately');
function display_tax_total_separately() {
    if (WC()->cart->get_taxes_total() > 0) {
        echo '<div class="tax-total-separate">';
        echo '<span class="text">' . esc_html(WC()->countries->tax_or_vat()) . '</span>';
        echo '<span>' . wc_price(WC()->cart->get_taxes_total()) . '</span>';
        echo '</div>';
    }
}

// Add CSS for tax display styling
add_action('wp_head', 'cart_tax_display_styles');
function cart_tax_display_styles() {
    if (is_cart() || is_checkout()) {
        ?>
        <style>
        .cart_subtotal small,
        .review_order_subtotal small {
            color: #666;
            font-style: italic;
            font-size: 12px;
        }

        .tax-total-separate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-top: 1px solid #eee;
            margin-top: 10px;
        }

        .tax-total-separate .text {
            font-weight: 600;
            color: #333;
        }

        .tax-total {
            display: block !important;
        }

        /* Hide the default tax display in totals */
        .cart_totals .tax-total,
        .review_order .tax-total {
            display: none !important;
        }
        </style>
        <?php
    }
}

// Newsletter subscription AJAX handler
add_action('wp_ajax_subscribe_newsletter', 'handle_newsletter_subscription');
add_action('wp_ajax_nopriv_subscribe_newsletter', 'handle_newsletter_subscription');

function handle_newsletter_subscription() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'newsletter_subscription')) {
        wp_send_json_error('Security check failed');
    }

    $email = sanitize_email($_POST['email']);
    $u = sanitize_text_field($_POST['u']);
    $id = sanitize_text_field($_POST['id']);

    // Validate email
    if (!is_email($email)) {
        wp_send_json_error('Invalid email address');
    }

    // Prepare Mailchimp submission
    $mailchimp_url = "https://mirumagency.us1.list-manage.com/subscribe/post-json";

    $data = array(
        'u' => $u,
        'id' => $id,
        'EMAIL' => $email,
        'subscribe' => 'Subscribe',
        'c' => 'jQuery' // JSONP callback
    );

    // Submit to Mailchimp using WordPress HTTP API
    $response = wp_remote_get($mailchimp_url . '?' . http_build_query($data), array(
        'timeout' => 30,
        'headers' => array(
            'User-Agent' => 'WordPress/' . get_bloginfo('version') . '; ' . home_url()
        )
    ));

    if (is_wp_error($response)) {
        error_log("Mailchimp API Error: " . $response->get_error_message());
        wp_send_json_error('Network error occurred. Please try again.');
    }

    $body = wp_remote_retrieve_body($response);

    // Clean up JSONP response if present
    $body = preg_replace('/^jQuery\d*_\d*\(/', '', $body);
    $body = preg_replace('/\);?$/', '', $body);

    // Log the response for debugging
    error_log("Mailchimp response for {$email}: " . $body);

    // Parse Mailchimp response more thoroughly
    if (strpos($body, '"result":"success"') !== false) {
        // Log successful subscription
        error_log("Newsletter subscription successful for: " . $email);
        wp_send_json_success('Successfully subscribed to newsletter!');
    } else if (strpos($body, 'already subscribed') !== false || strpos($body, 'is already a list member') !== false) {
        wp_send_json_error('This email address is already subscribed to our newsletter.');
    } else if (strpos($body, 'invalid email') !== false || strpos($body, 'Invalid email') !== false) {
        wp_send_json_error('Please enter a valid email address.');
    } else if (strpos($body, '"result":"error"') !== false) {
        // Try to extract the error message from Mailchimp response
        $json_response = json_decode($body, true);
        if ($json_response && isset($json_response['msg'])) {
            $error_message = strip_tags($json_response['msg']);
            wp_send_json_error($error_message);
        } else {
            wp_send_json_error('Subscription failed. Please try again.');
        }
    } else {
        // Log failed subscription with full response
        error_log("Newsletter subscription failed for: " . $email . " Full response: " . $body);
        wp_send_json_error('Unable to process subscription. Please try again later.');
    }
}


// add_action('woocommerce_single_product_summary', 'custom_disable_add_to_cart_buttons_for_all_except_allowed', 1);
// function custom_disable_add_to_cart_buttons_for_all_except_allowed() {
//     $allowed_product_ids = array(18910,18288,18929,18585,17172,18534,18204); // Replace with your allowed product IDs
//     global $product;

//     if (is_product() && !in_array($product->get_id(), $allowed_product_ids)) {
//         // Remove add to cart on single product page
//         remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
//         remove_action('woocommerce_single_variation', 'woocommerce_single_variation_add_to_cart_button', 20);
//         echo '<p style="color:red;">This product is not available for purchase online.</p>';
//     }
// }


// Define allowed product IDs only once
function get_allowed_product_ids() {
    return array(18910, 18288, 18929, 18585, 17172, 18534, 18204);
}

// 1. Prevent disallowed products from being purchasable
add_filter('woocommerce_is_purchasable', function($purchasable, $product) {
    if (!in_array($product->get_id(), get_allowed_product_ids())) {
        return false;
    }
    return $purchasable;
}, 10, 2);

// 2. Early hook to remove add to cart actions before they're added
add_action('wp', function() {
    if (!is_product()) {
        return;
    }

    global $post;
    if (!$post || !in_array($post->ID, get_allowed_product_ids())) {
        // Remove add to cart actions early
        remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
        remove_action('woocommerce_single_variation', 'woocommerce_single_variation_add_to_cart_button', 20);

        // Add custom message
        add_action('woocommerce_single_product_summary', function() {
            echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 20px 0;">
                    <strong>Notice:</strong> This product is not available for purchase online.
                  </div>';
        }, 30);
    }
});

// 3. Hide Add to Cart on shop/archive pages for disallowed products
add_filter('woocommerce_loop_add_to_cart_link', function($button, $product) {
    if (!in_array($product->get_id(), get_allowed_product_ids())) {
        return '<span style="color:red;">Not available for purchase</span>';
    }
    return $button;
}, 10, 2);



// add_action('woocommerce_after_shop_loop_item', 'custom_disable_add_to_cart_on_archive_for_all_except_allowed', 1);
// function custom_disable_add_to_cart_on_archive_for_all_except_allowed() {
//     $allowed_product_ids = array(18910,18288,18929,18585,18534,18204); // Replace with your allowed product IDs
//     global $product;

//     if (!in_array($product->get_id(), $allowed_product_ids)) {
//         remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10);
//     }
// }

// update mobile from my ac
add_action( 'woocommerce_save_account_details', 'save_billing_phone_account_details' );
function save_billing_phone_account_details( $user_id ) {
    if ( isset( $_POST['billing_phone'] ) ) {
        update_user_meta( $user_id, 'billing_phone', sanitize_text_field( $_POST['billing_phone'] ) );
    }
}
// change placeholder
add_filter( 'woocommerce_checkout_fields', 'custom_order_notes_placeholder' );
function custom_order_notes_placeholder( $fields ) {
    if ( isset( $fields['order']['order_comments'] ) ) {
        $fields['order']['order_comments']['placeholder'] = 'Notes about your order, e.g. special notes for delivery.'; // Change this text
    }
    return $fields;
}


// order separation
// ✅ 1. Add custom "Order Source" column to WooCommerce orders table
add_filter( 'manage_edit-shop_order_columns', 'custom_add_order_source_column', 20 );
function custom_add_order_source_column( $columns ) {
	error_log( 'Column hook loaded' );
    $new_columns = [];

    foreach ( $columns as $key => $label ) {
        $new_columns[ $key ] = $label;

        // Insert new column after order status
        if ( $key === 'order_status' ) {
            $new_columns['order_source'] = __( 'Order Source', 'hisense' );
        }
    }

    return $new_columns;
}

// ✅ 2. Display content inside the "Order Source" column
add_action( 'manage_shop_order_posts_custom_column', 'custom_render_order_source_column', 10, 2 );
function custom_render_order_source_column( $column, $post_id ) {
    if ( $column === 'order_source' ) {
        $order = wc_get_order( $post_id );

        if ( $order ) {
            $created_via = $order->get_created_via();

            if ( $created_via === 'checkout' ) {
                echo '<span style="color:green; font-weight:bold;">Online</span>';
            } elseif ( $created_via === 'admin' ) {
                echo '<span style="color:#00B3AC; font-weight:bold;">In Store</span>';
            } else {
                echo '<em>' . esc_html( ucfirst( $created_via ) ) . '</em>';
            }
        }
    }
}

// 1. Add download buttons
add_action( 'restrict_manage_posts', 'add_order_source_download_buttons' );
function add_order_source_download_buttons( $post_type ) {
    if ( $post_type !== 'shop_order' ) return;

    $admin_url = admin_url( 'admin-post.php' );
    ?>
    <a href="<?php echo esc_url( $admin_url . '?action=download_order_csv&source=checkout' ); ?>" class="button">Download Online Orders CSV</a>
    <?php
}

// 2. Handle CSV download
add_action( 'admin_post_download_order_csv', 'handle_order_csv_download' );
function handle_order_csv_download() {
    if ( ! current_user_can( 'manage_woocommerce' ) ) {
        wp_die( 'Permission denied' );
    }

    $source_filter = sanitize_text_field( $_GET['source'] ?? '' );
    if ( ! in_array( $source_filter, ['checkout', 'admin'], true ) ) {
        wp_die( 'Invalid source type' );
    }

    $args = array(
        'limit' => -1,
        'status' => 'any',
        'created_via' => $source_filter,
        'orderby' => 'date',
        'order' => 'DESC',
    );

    $orders = wc_get_orders( $args );

    header( 'Content-Type: text/csv' );
    header( 'Content-Disposition: attachment; filename="' . $source_filter . '-orders.csv"' );
    $output = fopen( 'php://output', 'w' );

    // CSV Header
    fputcsv( $output, ['Order ID', 'Date', 'Customer', 'Total', 'Payment Method'] );

    foreach ( $orders as $order ) {
        fputcsv( $output, [
            $order->get_id(),
            $order->get_date_created()->date( 'Y-m-d H:i:s' ),
            $order->get_formatted_billing_full_name(),
            'SAR ' . $order->get_total(),
            $order->get_payment_method_title(),
        ] );
    }

    fclose( $output );
    exit;
}


// add order prefix
add_filter( 'woocommerce_order_number', 'custom_prefix_based_on_order_source', 10, 2 );
function custom_prefix_based_on_order_source( $order_number, $order ) {
    if ( is_a( $order, 'WC_Order' ) ) {
        $source = $order->get_created_via();

        switch ( $source ) {
            case 'checkout':
                return 'WB-' . $order->get_id();
            case 'admin':
                return 'IN-' . $order->get_id();
            default:
                return strtoupper( $source ) . '-' . $order->get_id();
        }
    }

    return $order_number;
}
// Customize WCFM Store New Order Email Title and Content
add_filter( 'wcfmmp_store_new_order_email_heading', 'custom_store_new_order_email_heading', 10, 2 );

function custom_store_new_order_email_heading( $heading, $order ) {
    if ( is_a( $order, 'WC_Order' ) ) {
        $source = $order->get_created_via();

        if ( $source === 'checkout' ) {
            return __( 'New Online Order', 'wc-multivendor-marketplace' );
        } elseif ( $source === 'admin' ) {
            return __( 'New Store Order', 'wc-multivendor-marketplace' );
        }
    }

    // Default fallback
    return __( 'New Order', 'wc-multivendor-marketplace' );
}


// change dropdown in address

add_filter('woocommerce_checkout_fields', 'custom_override_checkout_city_field');

function custom_override_checkout_city_field($fields) {
    // Define your list of cities
    $cities = array(
        '' => __('Select a city', 'woocommerce'),
        'riyadh' => 'Riyadh',
        'jeddah' => 'Jeddah',
        'mecca' => 'Mecca',
        'medina' => 'Medina',
        'dammam' => 'Dammam',
        'taif' => 'Ta\'if',
        'tabuk' => 'Tabuk',
        'al-kharj' => 'Al-Kharj',
        'buraydah' => 'Buraydah',
        'khamis-mushait' => 'Khamis Mushait',
        'al-hofuf' => 'Al-Hofuf',
        'al-mubarraz' => 'Al-Mubarraz',
        'hafr-al-batin' => 'Hafr Al-Batin',
        'hail' => 'Ha\'il',
        'najran' => 'Najran',
        'abha' => 'Abha',
        'yanbu' => 'Yanbu',
        'khobar' => 'Khobar',
        'arar' => 'Arar',
        'sakaka' => 'Sakaka',
        'al-qatif' => 'Al-Qatif',
        'al-bahah' => 'Al-Bahah',
        'al-jubail' => 'Al-Jubail',
        'jizan' => 'Jizan',
        'al-ula' => 'Al-`Ula',
        'duba' => 'Duba',
        'al-wajh' => 'Al-Wajh',
        'qurayyat' => 'Al Qurayyat',
        'dhahran' => 'Dhahran'
    );

    // Replace the city field with a dropdown
    $fields['billing']['billing_city'] = array(
        'type'     => 'select',
        'label'    => __('Town / City', 'woocommerce'),
        'required' => true,
        'class'    => array('form-row-wide', 'address-field'),
        'priority' => 70,
        'options'  => $cities,
    );

    return $fields;
}

/**
 * Control Customer Invoice Email - Only send when order is completed for frontend orders
 */
function control_customer_invoice_email($enabled, $order, $email) {
    // Only modify the customer invoice email
    if ($email->id === 'customer_invoice') {
        // Check if order was created from frontend (not admin)
        $created_via = $order->get_created_via();

        // If order was created via checkout (frontend), only send email if status is completed
        if ($created_via === 'checkout') {
            // Only send invoice email if order status is completed
            if ($order->get_status() !== 'completed') {
                error_log("Blocking customer invoice email for frontend order {$order->get_id()} - status: {$order->get_status()}");
                return false; // Don't send the email
            } else {
                error_log("Allowing customer invoice email for frontend order {$order->get_id()} - status: completed");
                return true; // Send the email
            }
        }

        // For admin-created orders, send email normally
        error_log("Allowing customer invoice email for admin order {$order->get_id()} - created via: {$created_via}");
        return $enabled;
    }

    return $enabled;
}
add_filter('woocommerce_email_enabled_customer_invoice', 'control_customer_invoice_email', 10, 3);





// Method 2: Using WooCommerce mailer (alternative approach)
function trigger_store_email_via_mailer($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order #{$order_id} not found");
        return false;
    }
    
    // Reset email flags
    $order->delete_meta_data('_wcfmmp_order_email_triggered');
    $order->save();
    
    $mailer = WC()->mailer();
    $emails = $mailer->get_emails();
    
    if (isset($emails['WCFMmp_Email_Store_new_order'])) {
        $store_email = $emails['WCFMmp_Email_Store_new_order'];
        $store_email->trigger($order_id);
        error_log("Store email triggered via mailer for order #{$order_id}");
        return true;
    }
    
    error_log("Store email class not found in mailer");
    return false;
}


// =============================================================================
// CALLING METHODS - Choose one of these methods to trigger the email
// =============================================================================

// OPTION 1: Simple one-time call (runs once when you visit admin)
add_action('admin_init', 'trigger_store_email_by_url');
function trigger_store_email_by_url() {
    if (isset($_GET['trigger_store_email']) && current_user_can('manage_options')) {
        $order_id = intval($_GET['trigger_store_email']);
        if ($order_id > 0) {
            $result = trigger_store_email_via_mailer($order_id);
            if ($result) {
                wp_die("✅ Store email triggered successfully for order #{$order_id}");
            } else {
                wp_die("❌ Failed to trigger store email for order #{$order_id}");
            }
        }
    }
}
