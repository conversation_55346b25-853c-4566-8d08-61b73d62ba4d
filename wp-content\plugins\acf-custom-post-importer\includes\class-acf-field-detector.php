<?php
/**
 * ACF Field Detector Class
 * 
 * Detects and manages ACF fields for post types
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Importer_Field_Detector {
    
    /**
     * Get all ACF fields for a specific post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_acf_fields_for_post_type($post_type) {
        if (!function_exists('acf_get_field_groups')) {
            return array();
        }
        
        $field_groups = acf_get_field_groups(array(
            'post_type' => $post_type
        ));
        
        $all_fields = array();
        
        foreach ($field_groups as $field_group) {
            $fields = acf_get_fields($field_group);
            
            if ($fields) {
                foreach ($fields as $field) {
                    $formatted_field = $this->format_field_data($field, $field_group['title']);
                    $all_fields[] = $formatted_field;
                    
                    // Add sub-fields for complex field types
                    if (in_array($field['type'], array('repeater', 'group', 'flexible_content'))) {
                        $sub_fields = $this->get_sub_fields_recursive($field, $field_group['title'], $field['name']);
                        $all_fields = array_merge($all_fields, $sub_fields);
                    }
                }
            }
        }
        
        return $all_fields;
    }
    
    /**
     * Format field data for frontend use
     * 
     * @param array $field
     * @param string $group_title
     * @param string $parent_name
     * @return array
     */
    private function format_field_data($field, $group_title, $parent_name = '') {
        $field_name = $parent_name ? $parent_name . '_' . $field['name'] : $field['name'];
        
        return array(
            'key' => $field['key'],
            'name' => $field_name,
            'original_name' => $field['name'],
            'label' => $field['label'],
            'type' => $field['type'],
            'group' => $group_title,
            'parent' => $parent_name,
            'required' => isset($field['required']) ? $field['required'] : false,
            'choices' => $this->get_field_choices($field),
            'sub_fields' => $this->get_sub_fields($field),
            'instructions' => isset($field['instructions']) ? $field['instructions'] : '',
            'default_value' => isset($field['default_value']) ? $field['default_value'] : '',
            'importable' => $this->is_importable_field_type($field['type'])
        );
    }
    
    /**
     * Get field choices for select, radio, checkbox fields
     * 
     * @param array $field
     * @return array
     */
    private function get_field_choices($field) {
        $choices = array();
        
        if (isset($field['choices']) && is_array($field['choices'])) {
            $choices = $field['choices'];
        }
        
        // Handle special field types
        switch ($field['type']) {
            case 'true_false':
                $choices = array('0' => 'No', '1' => 'Yes');
                break;
                
            case 'post_object':
            case 'relationship':
                if (isset($field['post_type']) && is_array($field['post_type'])) {
                    $choices = $this->get_post_type_choices($field['post_type']);
                }
                break;
                
            case 'taxonomy':
                if (isset($field['taxonomy'])) {
                    $choices = $this->get_taxonomy_choices($field['taxonomy']);
                }
                break;
                
            case 'user':
                $choices = $this->get_user_choices();
                break;
        }
        
        return $choices;
    }
    
    /**
     * Get sub fields for repeater, group, flexible content fields
     * 
     * @param array $field
     * @return array
     */
    private function get_sub_fields($field) {
        $sub_fields = array();
        
        if (isset($field['sub_fields']) && is_array($field['sub_fields'])) {
            foreach ($field['sub_fields'] as $sub_field) {
                $sub_fields[] = array(
                    'key' => $sub_field['key'],
                    'name' => $sub_field['name'],
                    'label' => $sub_field['label'],
                    'type' => $sub_field['type'],
                    'required' => isset($sub_field['required']) ? $sub_field['required'] : false
                );
            }
        }
        
        // Handle flexible content layouts
        if ($field['type'] === 'flexible_content' && isset($field['layouts'])) {
            $layouts = array();
            foreach ($field['layouts'] as $layout) {
                $layout_fields = array();
                if (isset($layout['sub_fields'])) {
                    foreach ($layout['sub_fields'] as $layout_field) {
                        $layout_fields[] = array(
                            'key' => $layout_field['key'],
                            'name' => $layout_field['name'],
                            'label' => $layout_field['label'],
                            'type' => $layout_field['type']
                        );
                    }
                }
                $layouts[] = array(
                    'name' => $layout['name'],
                    'label' => $layout['label'],
                    'sub_fields' => $layout_fields
                );
            }
            $sub_fields = $layouts;
        }
        
        return $sub_fields;
    }
    
    /**
     * Get sub-fields recursively for complex field types
     * 
     * @param array $field
     * @param string $group_title
     * @param string $parent_name
     * @return array
     */
    private function get_sub_fields_recursive($field, $group_title, $parent_name = '') {
        $sub_fields = array();
        
        if (!isset($field['sub_fields']) || !is_array($field['sub_fields'])) {
            return $sub_fields;
        }
        
        foreach ($field['sub_fields'] as $sub_field) {
            $sub_field_name = $parent_name ? $parent_name . '_' . $sub_field['name'] : $sub_field['name'];
            $formatted_sub_field = $this->format_field_data($sub_field, $group_title, $sub_field_name);
            $sub_fields[] = $formatted_sub_field;
            
            // Recursively get sub-fields if this field has them
            if (in_array($sub_field['type'], array('repeater', 'group', 'flexible_content'))) {
                $nested_sub_fields = $this->get_sub_fields_recursive($sub_field, $group_title, $sub_field_name);
                $sub_fields = array_merge($sub_fields, $nested_sub_fields);
            }
        }
        
        return $sub_fields;
    }
    
    /**
     * Get choices for post object fields
     * 
     * @param array $post_types
     * @return array
     */
    private function get_post_type_choices($post_types) {
        $choices = array();
        
        foreach ($post_types as $post_type) {
            $posts = get_posts(array(
                'post_type' => $post_type,
                'posts_per_page' => 50,
                'post_status' => 'publish'
            ));
            
            foreach ($posts as $post) {
                $choices[$post->ID] = $post->post_title;
            }
        }
        
        return $choices;
    }
    
    /**
     * Get choices for taxonomy fields
     * 
     * @param string $taxonomy
     * @return array
     */
    private function get_taxonomy_choices($taxonomy) {
        $choices = array();
        $terms = get_terms(array(
            'taxonomy' => $taxonomy,
            'hide_empty' => false
        ));
        
        if (!is_wp_error($terms)) {
            foreach ($terms as $term) {
                $choices[$term->term_id] = $term->name;
            }
        }
        
        return $choices;
    }
    
    /**
     * Get choices for user fields
     * 
     * @return array
     */
    private function get_user_choices() {
        $choices = array();
        $users = get_users(array('number' => 50));
        
        foreach ($users as $user) {
            $choices[$user->ID] = $user->display_name;
        }
        
        return $choices;
    }
    
    /**
     * Get all standard WordPress fields for a post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_standard_fields($post_type) {
        $standard_fields = array(
            'ID' => array(
                'name' => 'ID',
                'label' => 'Post ID',
                'type' => 'number',
                'importable' => true
            ),
            'post_title' => array(
                'name' => 'post_title',
                'label' => 'Title',
                'type' => 'text',
                'importable' => true
            ),
            'post_content' => array(
                'name' => 'post_content',
                'label' => 'Content',
                'type' => 'textarea',
                'importable' => true
            ),
            'post_excerpt' => array(
                'name' => 'post_excerpt',
                'label' => 'Excerpt',
                'type' => 'textarea',
                'importable' => true
            ),
            'post_status' => array(
                'name' => 'post_status',
                'label' => 'Status',
                'type' => 'select',
                'importable' => true
            ),
            'post_date' => array(
                'name' => 'post_date',
                'label' => 'Date Published',
                'type' => 'date_time_picker',
                'importable' => true
            ),
            'post_modified' => array(
                'name' => 'post_modified',
                'label' => 'Date Modified',
                'type' => 'date_time_picker',
                'importable' => false
            )
        );
        
        // Add featured image for post types that support it
        if (post_type_supports($post_type, 'thumbnail')) {
            $standard_fields['featured_image'] = array(
                'name' => 'featured_image',
                'label' => 'Featured Image',
                'type' => 'image',
                'importable' => true
            );
        }
        
        // Add taxonomies for the post type
        $taxonomies = get_object_taxonomies($post_type, 'objects');
        foreach ($taxonomies as $taxonomy) {
            $standard_fields['tax_' . $taxonomy->name] = array(
                'name' => 'tax_' . $taxonomy->name,
                'label' => $taxonomy->labels->name,
                'type' => 'taxonomy',
                'importable' => true
            );
        }
        
        return $standard_fields;
    }
    
    /**
     * Check if field type supports import
     *
     * @param string $field_type
     * @return bool
     */
    public function is_importable_field_type($field_type) {
        $importable_types = array(
            'text', 'textarea', 'number', 'email', 'url', 'password',
            'select', 'checkbox', 'radio', 'true_false',
            'date_picker', 'date_time_picker', 'time_picker',
            'color_picker', 'image', 'file', 'gallery',
            'post_object', 'relationship', 'taxonomy', 'user',
            'wysiwyg', 'oembed', 'google_map', 'range',
            'repeater', 'group', 'flexible_content'
        );

        return in_array($field_type, $importable_types);
    }

    /**
     * Get combined fields (standard + ACF) for a post type
     *
     * @param string $post_type
     * @return array
     */
    public function get_all_fields_for_post_type($post_type) {
        $standard_fields = $this->get_standard_fields($post_type);
        $acf_fields = $this->get_acf_fields_for_post_type($post_type);

        return array(
            'standard' => $standard_fields,
            'acf' => $acf_fields
        );
    }
}
