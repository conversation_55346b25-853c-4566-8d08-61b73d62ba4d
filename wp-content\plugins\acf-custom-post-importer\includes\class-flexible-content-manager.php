<?php
/**
 * Flexible Content Manager Class
 * 
 * Handles creation and management of ACF flexible content items
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Flexible_Content_Manager {
    
    /**
     * Check if a field is a flexible content field
     * 
     * @param string $field_name
     * @return bool
     */
    public function is_flexible_content_field($field_name) {
        $field = get_field_object($field_name);
        return $field && $field['type'] === 'flexible_content';
    }
    
    /**
     * Get or create flexible content item
     * 
     * @param int $post_id
     * @param string $field_name
     * @param string $layout_name
     * @param array $sub_field_data
     * @return bool
     */
    public function ensure_flexible_content_item($post_id, $field_name, $layout_name, $sub_field_data = array()) {
        // Get current flexible content
        $current_content = get_field($field_name, $post_id, false);
        
        if (!is_array($current_content)) {
            $current_content = array();
        }
        
        // Check if layout already exists
        $layout_exists = false;
        foreach ($current_content as $item) {
            if (isset($item['acf_fc_layout']) && $item['acf_fc_layout'] === $layout_name) {
                $layout_exists = true;
                break;
            }
        }
        
        // If layout doesn't exist, create it
        if (!$layout_exists) {
            $new_item = array(
                'acf_fc_layout' => $layout_name
            );
            
            // Add sub field data if provided
            if (!empty($sub_field_data)) {
                $new_item = array_merge($new_item, $sub_field_data);
            } else {
                // Get default values for the layout
                $default_values = $this->get_layout_default_values($field_name, $layout_name);
                $new_item = array_merge($new_item, $default_values);
            }
            
            // Add the new item to the flexible content
            $current_content[] = $new_item;
            
            // Update the field
            return update_field($field_name, $current_content, $post_id);
        }
        
        return true;
    }
    
    /**
     * Get default values for a flexible content layout
     * 
     * @param string $field_name
     * @param string $layout_name
     * @return array
     */
    private function get_layout_default_values($field_name, $layout_name) {
        $field = get_field_object($field_name);
        $default_values = array();
        
        if (!$field || !isset($field['layouts'])) {
            return $default_values;
        }
        
        // Find the layout
        $layout = null;
        foreach ($field['layouts'] as $layout_config) {
            if ($layout_config['name'] === $layout_name) {
                $layout = $layout_config;
                break;
            }
        }
        
        if (!$layout || !isset($layout['sub_fields'])) {
            return $default_values;
        }
        
        // Get default values for sub fields
        foreach ($layout['sub_fields'] as $sub_field) {
            $default_value = $this->get_field_default_value($sub_field);
            if ($default_value !== null) {
                $default_values[$sub_field['name']] = $default_value;
            }
        }
        
        return $default_values;
    }
    
    /**
     * Get default value for a field based on its type
     * 
     * @param array $field
     * @return mixed
     */
    private function get_field_default_value($field) {
        if (isset($field['default_value']) && $field['default_value'] !== '') {
            return $field['default_value'];
        }
        
        switch ($field['type']) {
            case 'text':
            case 'textarea':
            case 'wysiwyg':
            case 'email':
            case 'url':
            case 'password':
                return '';
                
            case 'number':
            case 'range':
                return 0;
                
            case 'true_false':
                return false;
                
            case 'select':
            case 'radio':
                if (isset($field['choices']) && is_array($field['choices'])) {
                    $choices = array_keys($field['choices']);
                    return !empty($choices) ? $choices[0] : '';
                }
                return '';
                
            case 'checkbox':
                return array();
                
            case 'date_picker':
                return date('Y-m-d');
                
            case 'date_time_picker':
                return date('Y-m-d H:i:s');
                
            case 'time_picker':
                return date('H:i:s');
                
            case 'color_picker':
                return '#000000';
                
            case 'image':
            case 'file':
            case 'gallery':
                return null;
                
            case 'post_object':
            case 'relationship':
            case 'user':
            case 'taxonomy':
                return null;
                
            case 'repeater':
                return array();
                
            case 'group':
                // For group fields, get default values for sub fields
                $group_defaults = array();
                if (isset($field['sub_fields']) && is_array($field['sub_fields'])) {
                    foreach ($field['sub_fields'] as $sub_field) {
                        $default_value = $this->get_field_default_value($sub_field);
                        if ($default_value !== null) {
                            $group_defaults[$sub_field['name']] = $default_value;
                        }
                    }
                }
                return $group_defaults;
                
            case 'flexible_content':
                return array();
                
            default:
                return '';
        }
    }
    
    /**
     * Update flexible content item with new data
     * 
     * @param int $post_id
     * @param string $field_name
     * @param string $layout_name
     * @param array $sub_field_data
     * @param int $item_index Optional specific index to update
     * @return bool
     */
    public function update_flexible_content_item($post_id, $field_name, $layout_name, $sub_field_data, $item_index = null) {
        $current_content = get_field($field_name, $post_id, false);
        
        if (!is_array($current_content)) {
            $current_content = array();
        }
        
        $updated = false;
        
        // If specific index is provided, update that item
        if ($item_index !== null && isset($current_content[$item_index])) {
            if (isset($current_content[$item_index]['acf_fc_layout']) && 
                $current_content[$item_index]['acf_fc_layout'] === $layout_name) {
                $current_content[$item_index] = array_merge($current_content[$item_index], $sub_field_data);
                $updated = true;
            }
        } else {
            // Find and update the first matching layout
            foreach ($current_content as $index => $item) {
                if (isset($item['acf_fc_layout']) && $item['acf_fc_layout'] === $layout_name) {
                    $current_content[$index] = array_merge($current_content[$index], $sub_field_data);
                    $updated = true;
                    break;
                }
            }
        }
        
        if ($updated) {
            return update_field($field_name, $current_content, $post_id);
        }
        
        return false;
    }
    
    /**
     * Get all layouts for a flexible content field
     * 
     * @param string $field_name
     * @return array
     */
    public function get_flexible_content_layouts($field_name) {
        $field = get_field_object($field_name);
        $layouts = array();
        
        if ($field && isset($field['layouts']) && is_array($field['layouts'])) {
            foreach ($field['layouts'] as $layout) {
                $layouts[$layout['name']] = array(
                    'name' => $layout['name'],
                    'label' => $layout['label'],
                    'sub_fields' => isset($layout['sub_fields']) ? $layout['sub_fields'] : array()
                );
            }
        }
        
        return $layouts;
    }
    
    /**
     * Check if a flexible content layout exists for a post
     * 
     * @param int $post_id
     * @param string $field_name
     * @param string $layout_name
     * @return bool
     */
    public function layout_exists($post_id, $field_name, $layout_name) {
        $current_content = get_field($field_name, $post_id, false);
        
        if (!is_array($current_content)) {
            return false;
        }
        
        foreach ($current_content as $item) {
            if (isset($item['acf_fc_layout']) && $item['acf_fc_layout'] === $layout_name) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Remove a flexible content layout from a post
     * 
     * @param int $post_id
     * @param string $field_name
     * @param string $layout_name
     * @param int $item_index Optional specific index to remove
     * @return bool
     */
    public function remove_flexible_content_item($post_id, $field_name, $layout_name, $item_index = null) {
        $current_content = get_field($field_name, $post_id, false);
        
        if (!is_array($current_content)) {
            return false;
        }
        
        $removed = false;
        
        if ($item_index !== null && isset($current_content[$item_index])) {
            if (isset($current_content[$item_index]['acf_fc_layout']) && 
                $current_content[$item_index]['acf_fc_layout'] === $layout_name) {
                unset($current_content[$item_index]);
                $current_content = array_values($current_content); // Re-index array
                $removed = true;
            }
        } else {
            // Remove the first matching layout
            foreach ($current_content as $index => $item) {
                if (isset($item['acf_fc_layout']) && $item['acf_fc_layout'] === $layout_name) {
                    unset($current_content[$index]);
                    $current_content = array_values($current_content); // Re-index array
                    $removed = true;
                    break;
                }
            }
        }
        
        if ($removed) {
            return update_field($field_name, $current_content, $post_id);
        }
        
        return false;
    }
}
